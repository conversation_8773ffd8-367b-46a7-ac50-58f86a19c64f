
# Prompt 1
Please analyze my Echo application (an investment insights platform) and create comprehensive documentation of your findings. Specifically:

1. Review the entire codebase to understand the application's architecture, features, and functionality
2. Document what the application does, including:
   - Core features and capabilities (voice interaction, AI chat, stock analysis, etc.)
   - User interface components and user experience flow
   - Backend services and API integrations (OpenAI, Claude, financial data sources)
   - Data models and storage mechanisms
   - Key technical implementations (voice recognition, text-to-speech, conversation management)
3. Create a detailed specification document in the `spec` folder that captures:
   - Application overview and purpose
   - Feature descriptions
   - Technical architecture
   - User workflows
   - API endpoints and integrations
   - Any notable implementation details

Save your findings as a well-structured markdown document in the spec folder (create the folder if it doesn't exist). The documentation should be comprehensive enough for a new developer to understand what the application does and how it works.

