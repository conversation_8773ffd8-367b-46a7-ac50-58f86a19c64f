<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Attribution System Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .query-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .test-button {
            background: #ffee8c;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 600;
        }
        .test-button:hover {
            background: #ffe066;
        }
        .response-container {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #ffee8c;
        }
        .attribution-info {
            margin-top: 10px;
            padding: 10px;
            background: #e9ecef;
            border-radius: 4px;
            font-size: 12px;
        }
        .loading {
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <h1>🔍 Community Data Attribution System Test</h1>
    
    <div class="test-container">
        <h2>Test Queries</h2>
        <p>Try these queries to see how the system identifies and attributes community data:</p>
        
        <div style="margin-bottom: 15px;">
            <input type="text" class="query-input" id="queryInput" 
                   placeholder="Ask about market insights, valuations, or trading strategies..." 
                   value="What did GR say about valuations recently?">
            <button class="test-button" onclick="testQuery()">Test Attribution</button>
        </div>
        
        <div style="display: flex; gap: 10px; flex-wrap: wrap; margin-bottom: 15px;">
            <button class="test-button" onclick="setQuery('What did GR say about valuations recently?')">GR Valuations</button>
            <button class="test-button" onclick="setQuery('Tell me about S&P trading strategies')">S&P Trading</button>
            <button class="test-button" onclick="setQuery('What investment opportunities were discussed?')">Investment Opportunities</button>
            <button class="test-button" onclick="setQuery('Any recent market insights from the community?')">Market Insights</button>
        </div>
        
        <div id="responseContainer" style="display: none;">
            <h3>AI Response with Attribution</h3>
            <div id="responseContent"></div>
            <div id="attributionInfo" class="attribution-info"></div>
        </div>
    </div>

    <div class="test-container">
        <h2>How It Works</h2>
        <ul>
            <li><strong>🎯 Smart Loading:</strong> Relevant conversations are selected based on your query</li>
            <li><strong>🔍 Attribution Analysis:</strong> AI responses are analyzed to identify community-sourced information</li>
            <li><strong>👥 Visual Indicators:</strong> Community data is highlighted with user attribution</li>
            <li><strong>📊 Confidence Scoring:</strong> Each attribution includes a confidence score</li>
            <li><strong>🔗 Source Linking:</strong> Original community messages are referenced</li>
        </ul>
    </div>

    <script>
        function setQuery(query) {
            document.getElementById('queryInput').value = query;
        }

        async function testQuery() {
            const query = document.getElementById('queryInput').value;
            const responseContainer = document.getElementById('responseContainer');
            const responseContent = document.getElementById('responseContent');
            const attributionInfo = document.getElementById('attributionInfo');
            
            if (!query.trim()) {
                alert('Please enter a query');
                return;
            }
            
            responseContainer.style.display = 'block';
            responseContent.innerHTML = '<div class="loading">🤔 Analyzing query and loading community data...</div>';
            attributionInfo.innerHTML = '';
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: query,
                        context: 'stock_business_lead',
                        leadData: {},
                        conversationHistory: []
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    responseContent.innerHTML = `<strong>Response:</strong> ${result.response}`;
                    
                    if (result.attributions && result.attributions.length > 0) {
                        let attributionHTML = `<strong>🎯 Found ${result.attributions.length} community attribution(s):</strong><br><br>`;
                        
                        result.attributions.forEach((attr, index) => {
                            attributionHTML += `
                                <div style="margin-bottom: 15px; padding: 10px; background: white; border-radius: 4px;">
                                    <strong>Attribution ${index + 1}:</strong><br>
                                    <strong>Sentence:</strong> "${attr.sentence}"<br>
                                    <strong>Source:</strong> ${attr.source}<br>
                                    <strong>User:</strong> ${attr.user}<br>
                                    <strong>Timestamp:</strong> ${attr.timestamp}<br>
                                    <strong>Confidence:</strong> ${Math.round(attr.confidence)}%<br>
                                    <strong>Original:</strong> "${attr.originalContent}"
                                </div>
                            `;
                        });
                        
                        attributionInfo.innerHTML = attributionHTML;
                    } else {
                        attributionInfo.innerHTML = '<strong>ℹ️ No community data attributions found.</strong> This response appears to be based on general AI knowledge.';
                    }
                } else {
                    responseContent.innerHTML = `<strong>Error:</strong> ${result.error || 'Failed to get response'}`;
                    attributionInfo.innerHTML = '';
                }
            } catch (error) {
                responseContent.innerHTML = `<strong>Error:</strong> ${error.message}`;
                attributionInfo.innerHTML = '';
            }
        }
        
        // Allow Enter key to submit
        document.getElementById('queryInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                testQuery();
            }
        });
    </script>
</body>
</html>
