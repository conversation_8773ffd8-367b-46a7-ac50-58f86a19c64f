class AuthService {
  constructor() {
    this.isAuthenticated = true; // Access gate remains primary
  }

  async checkAuthStatus() {
    return true;
  }

  async makeAuthenticatedRequest(url, options = {}) {
    // Set appropriate headers based on content type
    let headers = { ...options.headers };

    // Attach JWT if present
    try {
      const token =
        (typeof window !== 'undefined' &&
          (window.__jwt || sessionStorage.getItem('jwt'))) ||
        null;
      if (token && !headers['Authorization']) {
        headers['Authorization'] = `Bearer ${token}`;
      }
    } catch (_) {
      // sessionStorage may be unavailable; ignore
    }

    // For JSON requests, ensure Content-Type is set
    if (
      options.body &&
      typeof options.body === 'string' &&
      !headers['Content-Type']
    ) {
      headers['Content-Type'] = 'application/json';
    }

    const response = await fetch(url, {
      ...options,
      headers,
      credentials: 'include',
    });

    return response;
  }
}

class VoiceLeadApp {
  constructor() {
    this.isRecording = false;
    this.isListening = false; // New: tracks if voice mode is active
    this.mediaRecorder = null;
    this.audioChunks = [];
    this.currentAudio = null;
    this.leadData = {};
    this.conversationHistory = [];
    this.isFirstMessage = true;
    this.currentSessionId = null;
    this.allConversations = [];
    this.userHasInteracted = false;
    this.statusHideTimer = null;
    this.sessionId = Date.now().toString(); // Unique session ID
    this.voiceActivationTimeout = null; // For automatic recording restart
    this.authService = new AuthService(); // Add authentication service
    this.audioContextUnlocked = false; // Track if audio context is unlocked
    this.flipCardsEnabled = true; // Enable flip by default to match menu label

    this.initializeApp();
  }

  async initializeApp() {
    // Check authentication first
    const isAuthenticated = await this.authService.checkAuthStatus();

    this.initializeElements();
    this.bindEvents();
    this.checkMicrophoneSupport();
    this.checkAudioFormats();
    this.showWelcomeMessage();
    this.startStatusHideTimer();

    // Ensure layout insets and scrolling are correct on load
    this.updateLayoutInsets();
    window.addEventListener('resize', () => this.updateLayoutInsets());
  }

  initializeElements() {
    this.micButton = document.getElementById('micButton');
    this.micIcon = document.getElementById('micIcon');
    this.micStatus = document.getElementById('micStatus');
    this.statusIndicator = document.getElementById('statusIndicator');
    this.conversationDisplay = document.getElementById('conversationDisplay');
    this.mainContentEl = document.querySelector('.main-content');
    this.voiceControls = document.querySelector('.voice-controls');
    this.headerEl = document.querySelector('.header');
    this.playButton = document.getElementById('playButton');
    this.predefinedPrompts = document.getElementById('predefinedPrompts');

    this.oldStopButton = document.getElementById('oldStopButton');
    this.audioPlayer = document.getElementById('audioPlayer');
    this.saveLeadBtn = document.getElementById('saveLeadBtn');
    this.exportLeadBtn = document.getElementById('exportLeadBtn');
    this.leadInfo = document.getElementById('leadInfo');
    this.aiSpeakingIndicator = document.getElementById('aiSpeakingIndicator');
    this.textInput = document.getElementById('textInput');
    this.sendButton = document.getElementById('sendButton');
    this.aiThinkingIndicator = document.getElementById('aiThinkingIndicator');

    // Stock preview card elements
    this.stockPreviewCard = document.getElementById('stockPreviewCard');
    this.previewCompanyName = document.getElementById('previewCompanyName');
    this.previewTicker = document.getElementById('previewTicker');
    this.previewPrice = document.getElementById('previewPrice');
    this.previewChange = document.getElementById('previewChange');
    this.previewMarketCap = document.getElementById('previewMarketCap');
    this.previewTarget = document.getElementById('previewTarget');

    // Check if critical elements exist
    if (!this.micButton) {
      console.error('Microphone button element not found!');
      return;
    }
    if (!this.micIcon) {
      console.error('Microphone icon element not found!');
      return;
    }

    // Log successful initialization
    console.log('✅ Microphone button and icon elements found and initialized');
    console.log('Microphone button:', this.micButton);
    console.log('Microphone icon:', this.micIcon);

    // Diagnostic check for button visibility
    this.checkButtonVisibility();

    // Menu elements
    this.burgerMenu = document.getElementById('burgerMenu');
    this.sideMenu = document.getElementById('sideMenu');
    this.menuOverlay = document.getElementById('menuOverlay');
    this.closeMenu = document.getElementById('closeMenu');
    this.menuHistoryBtn = document.getElementById('menuHistoryBtn');
    this.menuExportBtn = document.getElementById('menuExportBtn');
    this.menuClearBtn = document.getElementById('menuClearBtn');
    this.menuFlipToggle = document.getElementById('menuFlipToggle');
    this.flipCardsStateEl = document.getElementById('flipCardsState');

    // History elements
    this.historyModal = document.getElementById('historyModal');
    this.closeHistory = document.getElementById('closeHistory');
    this.historyContent = document.getElementById('historyContent');
    this.exportHistoryBtn = document.getElementById('exportHistory');
    this.clearHistoryBtn = document.getElementById('clearHistory');
    // Reflect flip state in menu label
    if (this.flipCardsStateEl) {
      this.flipCardsStateEl.textContent = this.flipCardsEnabled ? 'On' : 'Off';
    }
  }

  bindEvents() {
    if (this.micButton) {
      this.micButton.addEventListener('click', () => this.toggleRecording());
    }
    if (this.playButton) {
      this.playButton.addEventListener('click', () => this.playLastResponse());
    }

    if (this.oldStopButton) {
      this.oldStopButton.addEventListener('click', () => this.stopAudio());
    }
    if (this.saveLeadBtn) {
      this.saveLeadBtn.addEventListener('click', () => this.saveLead());
    }
    if (this.exportLeadBtn) {
      this.exportLeadBtn.addEventListener('click', () => this.exportLead());
    }

    // Text input event listeners
    if (this.sendButton) {
      this.sendButton.addEventListener('click', () => this.sendTextMessage());
    }
    if (this.textInput) {
      this.textInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          this.sendTextMessage();
        }
      });
    }

    // Menu event listeners
    this.burgerMenu.addEventListener('click', () => this.toggleMenu());
    this.closeMenu.addEventListener('click', () => this.hideMenu());
    this.menuOverlay.addEventListener('click', () => this.hideMenu());

    // Flip cards toggle
    if (this.menuFlipToggle) {
      this.menuFlipToggle.addEventListener('click', () =>
        this.toggleFlipMode()
      );
    }
    this.menuHistoryBtn.addEventListener('click', () => {
      this.hideMenu();
      this.showHistory();
    });
    this.menuExportBtn.addEventListener('click', () => {
      this.hideMenu();
      this.exportConversationHistory();
    });
    this.menuClearBtn.addEventListener('click', () => {
      this.hideMenu();
      this.clearConversationHistory();
    });

    // History modal event listeners
    this.closeHistory.addEventListener('click', () => this.hideHistory());

    // Bind explicit buttons inside the modal
    if (this.exportHistoryBtn) {
      this.exportHistoryBtn.addEventListener('click', () =>
        this.exportConversationHistory()
      );
    }
    if (this.clearHistoryBtn) {
      this.clearHistoryBtn.addEventListener('click', () =>
        this.clearConversationHistory()
      );
    }

    // Delegate clicks inside the modal content (CSP-safe)
    this.historyModal.addEventListener('click', (e) => {
      if (e.target === this.historyModal) {
        this.hideHistory();
        return;
      }
      const toggleEl = e.target.closest('.conversation-summary');
      const continueBtn = e.target.closest('.continue-btn');
      if (toggleEl && toggleEl.dataset.sessionId) {
        this.toggleConversationDetails(toggleEl.dataset.sessionId);
        return;
      }
      if (continueBtn && continueBtn.dataset.sessionId) {
        this.continueConversation(continueBtn.dataset.sessionId);
        return;
      }
    });

    // Bind predefined prompt buttons
    this.bindPromptButtons();

    // Setup test speech button
    this.setupTestSpeechButton();
  }

  setupTestSpeechButton() {
    const testBtn = document.getElementById('testSpeechBtn');
    if (testBtn) {
      testBtn.addEventListener('click', () => {
        console.log('🧪 Test Speech Button Clicked');

        // Test 1: Check if speech synthesis is available
        if (!('speechSynthesis' in window)) {
          alert('❌ Speech synthesis not supported in this browser');
          return;
        }

        console.log('✅ Speech synthesis is available');

        // Test 2: Get voices
        const voices = window.speechSynthesis.getVoices();
        console.log('🎵 Available voices:', voices.length);
        console.log('🎵 Voice list:', voices.map(v => `${v.name} (${v.lang}) - ${v.localService ? 'Local' : 'Remote'}`));

        if (voices.length === 0) {
          console.log('⚠️ No voices loaded yet, waiting for voiceschanged event...');
          window.speechSynthesis.addEventListener('voiceschanged', () => {
            const newVoices = window.speechSynthesis.getVoices();
            console.log('🎵 Voices loaded:', newVoices.length);
            console.log('🎵 Voice list:', newVoices.map(v => `${v.name} (${v.lang})`));
          }, { once: true });
        }

        // Test 3: Try simple speech
        try {
          window.speechSynthesis.cancel();
          const utterance = new SpeechSynthesisUtterance('Hello, this is a test of speech synthesis.');

          utterance.onstart = () => {
            console.log('✅ Speech started successfully!');
            testBtn.innerHTML = '🎵 Speaking...';
            testBtn.disabled = true;
          };

          utterance.onend = () => {
            console.log('✅ Speech completed successfully!');
            testBtn.innerHTML = '🧪 Test Speech Synthesis';
            testBtn.disabled = false;
          };

          utterance.onerror = (event) => {
            console.log('❌ Speech error:', event.error, event.type);
            alert(`❌ Speech error: ${event.error}`);
            testBtn.innerHTML = '🧪 Test Speech Synthesis';
            testBtn.disabled = false;
          };

          console.log('🎵 Calling speechSynthesis.speak()...');
          window.speechSynthesis.speak(utterance);

        } catch (error) {
          console.log('❌ Speech synthesis error:', error);
          alert(`❌ Speech synthesis error: ${error.message}`);
        }
      });
    }
  }

  // Toggle flip cards on/off from menu
  toggleFlipMode() {
    this.flipCardsEnabled = !this.flipCardsEnabled;
    if (this.flipCardsStateEl) {
      this.flipCardsStateEl.textContent = this.flipCardsEnabled ? 'On' : 'Off';
    }
    // Update existing cards
    const cards = document.querySelectorAll('.inline-stock-card');
    cards.forEach((card) => {
      // Hide/unhide flip buttons
      const flipButtons = card.querySelectorAll('.flip-toggle-button');
      flipButtons.forEach((btn) => {
        btn.style.display = this.flipCardsEnabled ? '' : 'none';
      });

      if (this.flipCardsEnabled) {
        card.classList.add('flip-card');
      } else {
        card.classList.remove('flip-card', 'flipped');
      }

      // Adjust faces and content
      this.adjustFlipStateForCard(card);
    });
  }

  // Bind predefined prompt buttons
  bindPromptButtons() {
    if (this.predefinedPrompts) {
      const promptButtons = this.predefinedPrompts.querySelectorAll('.prompt-button');
      promptButtons.forEach(button => {
        button.addEventListener('click', () => {
          const prompt = button.dataset.prompt;
          if (prompt) {
            this.handlePromptClick(prompt);
          }
        });
      });
    }
  }

  // Handle predefined prompt click
  handlePromptClick(prompt) {
    // Hide the predefined prompts immediately when user clicks
    this.hidePredefinedPrompts();

    // Fill the text input with the prompt
    if (this.textInput) {
      this.textInput.value = prompt;
    }

    // Send the message
    this.sendTextMessage();
  }

  // Hide predefined prompts
  hidePredefinedPrompts() {
    if (this.predefinedPrompts) {
      this.predefinedPrompts.classList.add('hidden');
      // Update layout after hiding prompts
      setTimeout(() => this.updateLayoutInsets(), 100);
    }
  }

  // Show predefined prompts (for empty state)
  showPredefinedPrompts() {
    if (this.predefinedPrompts) {
      this.predefinedPrompts.classList.remove('hidden');
      // Update layout after showing prompts
      setTimeout(() => this.updateLayoutInsets(), 100);
    }
  }

  // Apply flip-enabled/disabled layout to a given card
  adjustFlipStateForCard(cardDiv) {
    const frontContent = cardDiv.querySelector(
      '.flip-card-front .inline-stock-content'
    );
    const backFace = cardDiv.querySelector('.flip-card-back');

    if (!frontContent) return;

    if (this.flipCardsEnabled) {
      // Restore flip UI
      cardDiv.classList.remove('flip-disabled');
      cardDiv.classList.add('flip-card');
      // Remove any thesis clone on front
      const thesisClone = frontContent.querySelector(
        '.thesis-section.thesis-front'
      );
      if (thesisClone) thesisClone.remove();
      // Show back face
      if (backFace) backFace.style.display = '';
    } else {
      // Disable flip UI
      cardDiv.classList.remove('flip-card', 'flipped');
      cardDiv.classList.add('flip-disabled');
      // Hide back face
      if (backFace) backFace.style.display = 'none';
      // Move/clone thesis from back to front if not already present
      const existingFrontThesis = frontContent.querySelector(
        '.thesis-section.thesis-front'
      );
      if (!existingFrontThesis) {
        const backThesis = cardDiv.querySelector(
          '.flip-card-back .thesis-section'
        );
        if (backThesis) {
          const clone = backThesis.cloneNode(true);
          clone.classList.add('thesis-front');
          frontContent.appendChild(clone);
        }
      }
    }

    // Recompute height to front-only when disabled
    const inner = cardDiv.querySelector('.flip-card-inner');
    const front = cardDiv.querySelector('.flip-card-front');
    const back = cardDiv.querySelector('.flip-card-back');
    if (inner && front) {
      const h = this.flipCardsEnabled
        ? Math.max(front.offsetHeight, back?.offsetHeight || 0)
        : front.offsetHeight;
      inner.style.height = h + 'px';
    }
  }

  checkButtonVisibility() {
    if (!this.micButton) return;

    const rect = this.micButton.getBoundingClientRect();
    const styles = window.getComputedStyle(this.micButton);

    console.log('🔍 Microphone Button Diagnostic:');
    console.log('- Position:', rect);
    console.log('- Display:', styles.display);
    console.log('- Visibility:', styles.visibility);
    console.log('- Opacity:', styles.opacity);
    console.log('- Z-index:', styles.zIndex);
    console.log('- Disabled:', this.micButton.disabled);
    console.log('- Parent element:', this.micButton.parentElement);

    // Check if button is in viewport
    const isInViewport =
      rect.top >= 0 &&
      rect.left >= 0 &&
      rect.bottom <= window.innerHeight &&
      rect.right <= window.innerWidth;
    console.log('- In viewport:', isInViewport);

    // Check if button is actually visible
    const isVisible =
      rect.width > 0 &&
      rect.height > 0 &&
      styles.display !== 'none' &&
      styles.visibility !== 'hidden' &&
      parseFloat(styles.opacity) > 0;
    console.log('- Actually visible:', isVisible);
  }

  checkAudioFormats() {
    console.log('=== Audio Format Support Check ===');
    const formats = [
      'audio/webm',
      'audio/webm;codecs=opus',
      'audio/mp4',
      'audio/ogg',
      'audio/wav',
      'audio/mpeg',
    ];

    formats.forEach((format) => {
      const supported = MediaRecorder.isTypeSupported(format);
      console.log(
        `${format}: ${supported ? '✓ Supported' : '✗ Not supported'}`
      );
    });
    console.log('=== End Audio Format Check ===');
  }

  async checkMicrophoneSupport() {
    console.log('🎤 Checking microphone support...');
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach((track) => track.stop());
      console.log('✅ Microphone access granted');
      this.updateStatus('Microphone ready - click to start');
    } catch (error) {
      console.error('❌ Microphone error:', error);
      if (this.micButton) {
        this.micButton.disabled = true;
        console.log('🔒 Microphone button disabled due to error');
      }
      console.error('Microphone error:', error);

      let errorMessage =
        'Microphone access is required for voice functionality.';
      let details = error.message;

      if (error.name === 'NotAllowedError') {
        errorMessage =
          'Microphone access was denied. Please allow microphone access and refresh the page.';
        details =
          "Click the microphone icon in your browser's address bar to allow access.";
      } else if (error.name === 'NotFoundError') {
        errorMessage =
          'No microphone found. Please connect a microphone and refresh the page.';
      }

      this.showError('Microphone Not Available', errorMessage, details);
    }
  }

  startStatusHideTimer() {
    // Hide status indicator after 5 seconds
    this.statusHideTimer = setTimeout(() => {
      this.statusIndicator.classList.add('hidden');
    }, 5000);
  }

  showWelcomeMessage() {
    console.log('🎵 showWelcomeMessage() called');

    // Show predefined prompts initially
    this.showPredefinedPrompts();

    // Show initial voice introduction from Sarah
    setTimeout(async () => {
      console.log('🎵 showWelcomeMessage timeout executing');
      // Separate visual and audio messages
      const visualMessage = "Hey Mark,\n\nLets cut through the noise, together.";
      const spokenMessage = "Let's cut through the noise, together. I'm here to surface the most relevant insights from our investor network. Would you like today's highlights, or to delve deeper on a stock you've been thinking about?";

      console.log('🎵 Adding welcome message to display');

      // Inject into the existing centered welcome container for visual display
      try {
        const wrapper =
          this.conversationDisplay?.querySelector('.welcome-message');
        if (wrapper) {
          const html = `<i class="fas fa-robot"></i><p>${visualMessage}</p>`;
          wrapper.innerHTML = html;
        }
      } catch (_) {
        // Fallback: no-op
      }

      // Wait 2 seconds, then try to play the welcome message using OpenAI TTS
      console.log('🎵 Scheduling OpenAI TTS welcome audio playback after 2 second delay...');
      setTimeout(async () => {
        console.log('🎵 2-second timeout reached - attempting OpenAI TTS welcome message playback...');
        console.log('🎵 Welcome message text:', spokenMessage);

        try {
          console.log('🎵 Calling attemptOpenAIWelcomePlayback...');
          const playSuccess = await this.attemptOpenAIWelcomePlayback(spokenMessage);
          console.log('🎵 attemptOpenAIWelcomePlayback returned:', playSuccess);

          if (!playSuccess) {
            console.log('⚠️ Auto-play blocked or failed, storing for later playback');
            // Store the welcome message for playback when user first interacts
            this.pendingWelcomeAudio = spokenMessage;
            console.log('🎵 Welcome message stored for later playback:', spokenMessage.substring(0, 50) + '...');

            // Show enable audio button after a 2-second delay
            setTimeout(() => {
              console.log('🎵 Showing enable audio button...');
              this.showEnableAudioButton();
            }, 2000);
          } else {
            console.log('✅ Welcome message auto-play successful!');
          }
        } catch (error) {
          console.log('❌ Welcome audio playback failed with error:', error.message);
          console.log('❌ Error stack:', error.stack);
          // Store for later playback when user interacts
          this.pendingWelcomeAudio = spokenMessage;
          console.log('🎵 Welcome message stored for later playback due to error');

          // Show enable audio button after a 2-second delay
          setTimeout(() => {
            console.log('🎵 Showing enable audio button due to error...');
            this.showEnableAudioButton();
          }, 2000);
        }
      }, 2000); // Wait 2 seconds after the message appears
    }, 1000);
  }

  showEnableAudioButton() {
    // Check if button already exists
    if (document.querySelector('.enable-audio-container')) {
      return;
    }

    // Add an enable audio button to the UI
    const enableAudioContainer = document.createElement('div');
    enableAudioContainer.className = 'enable-audio-container';

    const enableAudioBtn = document.createElement('button');
    enableAudioBtn.className = 'enable-audio-btn';
    enableAudioBtn.innerHTML = '🔊 Click to enable audio and hear my welcome message';

    const enableAudioNote = document.createElement('p');
    enableAudioNote.className = 'enable-audio-note';
    enableAudioNote.innerHTML = 'Your browser blocked auto-play audio for security';

    enableAudioContainer.appendChild(enableAudioBtn);
    enableAudioContainer.appendChild(enableAudioNote);

    // Add direct event listener with immediate audio loading
    enableAudioBtn.addEventListener('click', (event) => {
      console.log('🎵 Direct click event triggered - starting immediate audio load');

      // Prevent any default behavior
      event.preventDefault();
      event.stopPropagation();

      // Update button immediately in the click handler
      enableAudioBtn.innerHTML = '🔄 Loading audio...';
      enableAudioBtn.disabled = true;

      // CRITICAL: Start TTS API call IMMEDIATELY in the click handler
      // This ensures the audio loading happens in direct response to user interaction
      this.loadWelcomeAudioImmediately();
    });

    // Add to the top of messages or after welcome message
    const messagesContainer = document.getElementById('messages');
    if (messagesContainer) {
      messagesContainer.appendChild(enableAudioContainer);
      messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
  }

  loadWelcomeAudioImmediately() {
    console.log('🎵 loadWelcomeAudioImmediately called - starting OpenAI TTS API call in click handler');

    if (!this.pendingWelcomeAudio) {
      console.log('❌ No pending welcome audio');
      this.resetEnableButton();
      return;
    }

    // Get the dedicated welcome audio element
    const welcomeAudio = document.getElementById('welcomeAudioPlayer');
    if (!welcomeAudio) {
      console.log('❌ Welcome audio element not found');
      this.resetEnableButton();
      return;
    }

    // Start the OpenAI TTS API call immediately in the click handler
    fetch('/api/text-to-speech', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        text: this.pendingWelcomeAudio,
        voice: 'alloy' // Use alloy voice - sounds natural
      }),
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`OpenAI TTS API request failed: ${response.status}`);
      }
      return response.blob();
    })
    .then(audioBlob => {
      console.log('✅ OpenAI TTS API response received, creating audio URL...');
      const audioUrl = URL.createObjectURL(audioBlob);

      // Set the audio source and play immediately
      welcomeAudio.src = audioUrl;
      welcomeAudio.volume = 1.0;

      // Set up event handlers
      welcomeAudio.onloadeddata = () => {
        console.log('✅ Welcome audio data loaded, attempting to play...');

        // Try to play immediately
        const playPromise = welcomeAudio.play();

        if (playPromise !== undefined) {
          playPromise.then(() => {
            console.log('✅ OpenAI TTS welcome audio playing successfully!');

            // Show speaking animation
            this.showAIThinking('Sarah is speaking...');

            // Remove the enable audio container after successful playback
            const enableContainer = document.querySelector('.enable-audio-container');
            if (enableContainer) {
              enableContainer.remove();
            }
          }).catch((error) => {
            console.log('❌ Welcome audio play failed:', error.name, error.message);
            this.resetEnableButton();
          });
        }
      };

      welcomeAudio.onerror = (error) => {
        console.log('❌ Welcome audio element error:', error);
        this.resetEnableButton();
      };

      welcomeAudio.onended = () => {
        console.log('✅ Welcome audio playback completed');
        // Hide speaking animation
        this.hideAIThinking();
        // Clean up
        URL.revokeObjectURL(audioUrl);
      };

      // Load the audio
      welcomeAudio.load();

      this.pendingWelcomeAudio = null; // Clear after setting up
    })
    .catch(error => {
      console.log('❌ OpenAI TTS API failed:', error);
      this.resetEnableButton();
    });
  }

  enableAudio() {
    console.log('🎵 User clicked enable audio - trying immediate speech synthesis');

    // CRITICAL: Try speech synthesis IMMEDIATELY in the click handler
    if (this.pendingWelcomeAudio && 'speechSynthesis' in window) {
      console.log('🎵 Attempting immediate speech synthesis...');

      try {
        // Stop any existing speech
        window.speechSynthesis.cancel();

        // Create utterance immediately
        const utterance = new SpeechSynthesisUtterance(this.pendingWelcomeAudio);

        // Get voices
        const voices = window.speechSynthesis.getVoices();
        console.log('🎵 Available voices:', voices.map(v => v.name));

        // Try to find Karen voice
        const karenVoice = voices.find(voice => voice.name.toLowerCase().includes('karen'));
        if (karenVoice) {
          utterance.voice = karenVoice;
          console.log('🎵 Using Karen voice:', karenVoice.name);
        } else {
          console.log('🎵 Karen voice not found, using default voice');
        }

        // Configure speech
        utterance.rate = 0.9;
        utterance.pitch = 1.0;
        utterance.volume = 1.0;

        // Handle events
        utterance.onstart = () => {
          console.log('✅ Speech synthesis started successfully!');
          // Update button to show it's working
          const enableBtn = document.querySelector('.enable-audio-btn');
          if (enableBtn) {
            enableBtn.innerHTML = '🎵 Playing...';
            enableBtn.disabled = true;
          }
        };

        utterance.onend = () => {
          console.log('✅ Speech synthesis completed!');
          // Remove the enable audio container
          const enableContainer = document.querySelector('.enable-audio-container');
          if (enableContainer) {
            enableContainer.remove();
          }
        };

        utterance.onerror = (event) => {
          console.log('❌ Speech synthesis error:', event.error);
          // If speech synthesis fails, try TTS API as backup
          this.fallbackToTTSAPI();
        };

        // Speak immediately - this MUST be synchronous in the click handler
        window.speechSynthesis.speak(utterance);
        this.pendingWelcomeAudio = null;

        console.log('🎵 Speech synthesis command issued successfully');
        return; // Exit early if speech synthesis works

      } catch (error) {
        console.log('❌ Immediate speech synthesis failed:', error);
        // Fall through to TTS API backup
      }
    }

    // If we get here, speech synthesis failed or isn't available
    console.log('🎵 Speech synthesis not available, using TTS API backup');
    this.fallbackToTTSAPI();
  }

  async fallbackToTTSAPI() {
    if (!this.pendingWelcomeAudio) return;

    console.log('🎵 Falling back to OpenAI TTS API...');

    // Update button to show loading
    const enableBtn = document.querySelector('.enable-audio-btn');
    if (enableBtn) {
      enableBtn.innerHTML = '🔄 Loading audio...';
      enableBtn.disabled = true;
    }

    try {
      // First unlock audio context
      const silentAudio = new Audio(
        'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT'
      );
      silentAudio.volume = 0;
      await silentAudio.play().catch(() => {});

      const response = await fetch('/api/text-to-speech', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: this.pendingWelcomeAudio,
          voice: 'alloy' // Use alloy voice for consistency
        }),
      });

      if (response.ok) {
        const audioBlob = await response.blob();
        const audioUrl = URL.createObjectURL(audioBlob);

        const audioElement = new Audio(audioUrl);
        audioElement.volume = 1.0;

        // Set up event handlers
        audioElement.onplay = () => {
          console.log('✅ OpenAI TTS fallback audio playing!');
          this.showAIThinking('Sarah is speaking...');
        };

        audioElement.onended = () => {
          console.log('✅ OpenAI TTS fallback audio completed');
          this.hideAIThinking();
          URL.revokeObjectURL(audioUrl);
        };

        try {
          await audioElement.play();
          console.log('✅ OpenAI TTS API fallback successful!');

          const enableContainer = document.querySelector('.enable-audio-container');
          if (enableContainer) {
            enableContainer.remove();
          }
        } catch (playError) {
          console.log('❌ OpenAI TTS API play failed:', playError);
          this.resetEnableButton();
        }

        this.pendingWelcomeAudio = null;
      } else {
        throw new Error(`OpenAI TTS API request failed: ${response.status}`);
      }
    } catch (error) {
      console.log('❌ OpenAI TTS API fallback failed:', error);
      this.resetEnableButton();
    }
  }

  resetEnableButton() {
    const enableBtn = document.querySelector('.enable-audio-btn');
    if (enableBtn) {
      enableBtn.innerHTML = '🔊 Click to enable audio and hear my welcome message';
      enableBtn.disabled = false;
    }
  }

  async attemptOpenAIWelcomePlayback(welcomeText) {
    return new Promise(async (resolve) => {
      try {
        console.log('🎵 attemptOpenAIWelcomePlayback called with text length:', welcomeText.length);
        console.log('🎵 Making OpenAI TTS API request...');

        // Get the dedicated welcome audio element
        const welcomeAudio = document.getElementById('welcomeAudioPlayer');
        if (!welcomeAudio) {
          console.log('❌ Welcome audio element not found in DOM');
          resolve(false);
          return;
        }
        console.log('✅ Welcome audio element found:', welcomeAudio);

        // Call OpenAI TTS API
        console.log('🎵 Sending fetch request to /api/text-to-speech...');
        const response = await fetch('/api/text-to-speech', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            text: welcomeText,
            voice: 'alloy' // Use alloy voice - sounds natural
          }),
        });

        console.log('🎵 TTS API response received:', response.status, response.statusText);

        if (!response.ok) {
          const errorText = await response.text();
          console.log('❌ TTS API error response:', errorText);
          throw new Error(`TTS API request failed: ${response.status} - ${errorText}`);
        }

        const audioBlob = await response.blob();
        console.log('✅ OpenAI TTS API audio blob received, size:', audioBlob.size, 'bytes');
        const audioUrl = URL.createObjectURL(audioBlob);
        console.log('✅ Audio URL created:', audioUrl);

        // Set the audio source
        welcomeAudio.src = audioUrl;
        welcomeAudio.volume = 1.0;
        console.log('✅ Audio source set, volume:', welcomeAudio.volume);

        // Set up event handlers
        welcomeAudio.onloadeddata = () => {
          console.log('✅ Welcome audio data loaded, attempting auto-play...');

          // Try to play immediately
          const playPromise = welcomeAudio.play();
          console.log('🎵 Play promise:', playPromise);

          if (playPromise !== undefined) {
            playPromise.then(() => {
              console.log('✅ OpenAI TTS welcome audio playing successfully!');
              // Show speaking animation
              this.showAIThinking('Sarah is speaking...');
              resolve(true);
            }).catch((error) => {
              console.log('❌ Welcome audio auto-play failed:', error.name, error.message);
              console.log('❌ Error details:', error);
              resolve(false);
            });
          } else {
            // Older browsers without promise support
            console.log('✅ Welcome audio playing (no promise support)');
            this.showAIThinking('Sarah is speaking...');
            resolve(true);
          }
        };

        welcomeAudio.onerror = (error) => {
          console.log('❌ Welcome audio element error:', error);
          resolve(false);
        };

        welcomeAudio.onended = () => {
          console.log('✅ Welcome audio playback completed');
          // Hide speaking animation
          this.hideAIThinking();
          // Clean up
          URL.revokeObjectURL(audioUrl);
        };

        // Load the audio
        console.log('🎵 Loading audio...');
        welcomeAudio.load();

        console.log('✅ Welcome audio setup complete, waiting for load event...');

      } catch (error) {
        console.log('❌ OpenAI TTS welcome playback error:', error.message);
        console.log('❌ Error stack:', error.stack);
        resolve(false);
      }
    });
  }

  async attemptImmediateWelcomePlayback(welcomeText) {
    try {
      console.log('🎵 Trying welcome message playback...');

      // For welcome message, skip TTS API and go directly to Web Speech API
      // to avoid error messages when running without backend
      if ('speechSynthesis' in window) {
        console.log('🎵 Using Web Speech API for welcome message...');

        // Wait for voices to load
        await this.waitForVoices();

        const utterance = new SpeechSynthesisUtterance(welcomeText);
        utterance.rate = 0.9;
        utterance.pitch = 1.1;
        utterance.volume = 0.8;

        // Try to find a female voice
        const voices = speechSynthesis.getVoices();
        console.log('🎵 Available voices:', voices.length);

        const femaleVoice = voices.find(voice =>
          voice.name.toLowerCase().includes('female') ||
          voice.name.toLowerCase().includes('samantha') ||
          voice.name.toLowerCase().includes('karen') ||
          voice.name.toLowerCase().includes('sarah') ||
          voice.name.toLowerCase().includes('zira') ||
          voice.name.toLowerCase().includes('hazel')
        );

        if (femaleVoice) {
          console.log('🎵 Using voice:', femaleVoice.name);
          utterance.voice = femaleVoice;
        } else {
          console.log('🎵 Using default voice');
        }

        // Add event listeners for debugging
        utterance.onstart = () => console.log('🎵 Speech started');
        utterance.onend = () => console.log('🎵 Speech ended');
        utterance.onerror = (e) => console.log('❌ Speech error:', e.error);

        speechSynthesis.speak(utterance);
        console.log('✅ Welcome message queued for speech');
        return true;
      }

      console.log('❌ Web Speech API not available');
      return false;
    } catch (error) {
      console.log('❌ Welcome playback error:', error.message);
      return false;
    }
  }

  // Helper method to wait for voices to load
  waitForVoices() {
    return new Promise((resolve) => {
      const voices = speechSynthesis.getVoices();
      if (voices.length > 0) {
        resolve(voices);
        return;
      }

      const voicesChangedHandler = () => {
        const voices = speechSynthesis.getVoices();
        if (voices.length > 0) {
          speechSynthesis.removeEventListener('voiceschanged', voicesChangedHandler);
          resolve(voices);
        }
      };

      speechSynthesis.addEventListener('voiceschanged', voicesChangedHandler);

      // Fallback timeout
      setTimeout(() => {
        speechSynthesis.removeEventListener('voiceschanged', voicesChangedHandler);
        resolve(speechSynthesis.getVoices());
      }, 1000);
    });
  }

  async toggleRecording() {
    // Mark that user has interacted with the page
    this.userHasInteracted = true;

    // Create audio context for auto-play (must be done in user interaction)
    this.prepareAudioContext();

    // Play pending welcome audio if available
    this.playPendingWelcomeAudio();

    if (this.isListening || this.isRecording) {
      // Stop all active voice activities
      this.stopEverything();
    } else {
      // Turn ON voice mode
      await this.startVoiceMode();
    }
  }

  prepareAudioContext() {
    // Create and prepare an audio element during user interaction
    // This ensures we have a "blessed" audio element that can auto-play
    if (!this.audioContextUnlocked) {
      // Create a reusable audio element in the user interaction context
      this.audioPlayer = document.getElementById('audioPlayer') || new Audio();

      // Multiple attempts to unlock audio context for mobile
      const unlockAudio = async () => {
        try {
          // Method 1: Create a simple audio element and try to play it
          const testAudio = new Audio();
          testAudio.volume = 0.01;
          testAudio.muted = true;

          // Create a simple beep sound programmatically
          if (window.AudioContext || window.webkitAudioContext) {
            const AudioContextClass = window.AudioContext || window.webkitAudioContext;
            const audioContext = new AudioContextClass();

            // Create a short beep
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(440, audioContext.currentTime);
            gainNode.gain.setValueAtTime(0, audioContext.currentTime);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.01);

            if (audioContext.state === 'suspended') {
              await audioContext.resume();
            }

            console.log('✅ Audio context unlocked with Web Audio API');
            this.audioContextUnlocked = true;
            return;
          }

          console.log('✅ Audio context preparation complete');
        } catch (error) {
          console.log('⚠️ Audio context unlock attempt failed:', error.message);
          // Don't fail completely, just mark as unlocked and try anyway
          this.audioContextUnlocked = true;
        }
      };

      unlockAudio();
      this.audioContextUnlocked = true;
    }
  }

  playPendingWelcomeAudio() {
    // Play the welcome audio if it's pending and user has now interacted
    console.log('🎵 Checking for pending welcome audio...', {
      hasPendingAudio: !!this.pendingWelcomeAudio,
      userHasInteracted: this.userHasInteracted,
      pendingText: this.pendingWelcomeAudio ? this.pendingWelcomeAudio.substring(0, 50) + '...' : 'none'
    });

    if (this.pendingWelcomeAudio && this.userHasInteracted) {
      console.log('🎵 Playing pending welcome audio...');
      this.textToSpeech(this.pendingWelcomeAudio);
      this.pendingWelcomeAudio = null; // Clear it so it only plays once
    }
  }

  prepareAudioForTTS() {
    // Create a "blessed" audio element during active user interaction
    // This element will be able to auto-play TTS responses
    if (!this.ttsAudioElement) {
      this.ttsAudioElement = new Audio();
      this.ttsAudioElement.preload = 'auto';

      // Mobile-specific attributes for better autoplay support
      this.ttsAudioElement.setAttribute('playsinline', 'true');
      this.ttsAudioElement.setAttribute('webkit-playsinline', 'true');

      // Try to unlock the audio element immediately
      this.ttsAudioElement.volume = 0;
      this.ttsAudioElement
        .play()
        .then(() => {
          this.ttsAudioElement.pause();
          this.ttsAudioElement.volume = 1;
          console.log('✅ TTS audio element unlocked successfully');
        })
        .catch(() => {
          console.log(
            '⚠️ TTS audio unlock failed, will try alternative methods'
          );
        });

      // Set up event handlers
      this.ttsAudioElement.addEventListener('ended', () => {
        if (this.isListening) {
          // If in listening mode, restart recording after AI finishes speaking
          this.updateUI('listening');
          this.showAIThinking('Listening...');
          this.voiceActivationTimeout = setTimeout(async () => {
            if (this.isListening && !this.isRecording) {
              await this.startRecording();
            }
          }, 1000);
        } else {
          this.hideAIThinking();
          this.updateUI('ready');
        }
      });

      this.ttsAudioElement.addEventListener('error', (e) => {
        console.error('TTS Audio error:', e);
        this.handleAutoPlayBlocked();
      });

      console.log('🎵 TTS audio element prepared for auto-play');
    }

    // Also try to unlock audio context if not already done
    if (!this.audioContextUnlocked) {
      console.log('🎵 Attempting to unlock audio context during user interaction...');
      this.unlockAudioContextSimple();
    }
  }

  unlockAudioContextSimple() {
    try {
      if (window.AudioContext || window.webkitAudioContext) {
        const AudioContextClass = window.AudioContext || window.webkitAudioContext;
        const audioContext = new AudioContextClass();

        if (audioContext.state === 'suspended') {
          audioContext.resume().then(() => {
            console.log('✅ Audio context resumed successfully');
            this.audioContextUnlocked = true;
            audioContext.close();
          }).catch((error) => {
            console.log('⚠️ Audio context resume failed:', error.message);
            this.audioContextUnlocked = true; // Try anyway
          });
        } else {
          console.log('✅ Audio context already running');
          this.audioContextUnlocked = true;
          audioContext.close();
        }
      } else {
        console.log('⚠️ No AudioContext available');
        this.audioContextUnlocked = true; // Try anyway
      }
    } catch (error) {
      console.log('⚠️ Audio context unlock error:', error.message);
      this.audioContextUnlocked = true; // Try anyway
    }
  }

  async startVoiceMode() {
    this.isListening = true;
    this.updateUI('listening');
    this.updateStatus('Voice mode ON - Listening for speech...');

    // Show blob animation when voice mode starts
    this.showAIThinking('Listening...');

    // Start the first recording session
    await this.startRecording();
  }

  stopVoiceMode() {
    this.isListening = false;

    // Clear any pending restart timeout
    if (this.voiceActivationTimeout) {
      clearTimeout(this.voiceActivationTimeout);
      this.voiceActivationTimeout = null;
    }

    // Stop current recording if active
    if (this.isRecording) {
      this.stopRecording();
    }

    // Hide blob animation when voice mode stops
    this.hideAIThinking();

    this.updateUI('ready');
    this.updateStatus('Voice mode OFF - Click to activate');
  }

  async startRecording() {
    try {
      // Pre-create audio element for TTS response during user interaction
      this.prepareAudioForTTS();

      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: 44100, // Changed to more compatible sample rate
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
        },
      });

      // Try to use a supported audio format with better compatibility
      let options = {};
      let mimeType = '';

      // Check for supported formats in order of preference
      if (MediaRecorder.isTypeSupported('audio/webm')) {
        options = { mimeType: 'audio/webm' };
        mimeType = 'audio/webm';
      } else if (MediaRecorder.isTypeSupported('audio/mp4')) {
        options = { mimeType: 'audio/mp4' };
        mimeType = 'audio/mp4';
      } else if (MediaRecorder.isTypeSupported('audio/ogg')) {
        options = { mimeType: 'audio/ogg' };
        mimeType = 'audio/ogg';
      } else {
        // Fallback to default (usually works)
        console.log('Using default MediaRecorder format');
        mimeType = 'audio/wav'; // Default assumption
      }

      console.log('Using audio format:', mimeType);

      this.mediaRecorder = new MediaRecorder(stream, options);
      this.audioChunks = [];
      this.recordedMimeType = mimeType; // Store for later use

      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.audioChunks.push(event.data);
        }
      };

      this.mediaRecorder.onstop = () => {
        this.processRecording();
      };

      this.mediaRecorder.start(1000); // Record in 1-second chunks
      this.isRecording = true;

      this.updateUI('recording');
      this.updateStatus('Listening... Click again to stop');
    } catch (error) {
      console.error('Recording error:', error);
      this.showError(
        'Recording Failed',
        'Could not start recording. Please check microphone permissions.',
        `Error: ${error.message}\nMake sure your browser has microphone access.`
      );

      // If in listening mode, try to restart after error
      if (this.isListening) {
        this.voiceActivationTimeout = setTimeout(async () => {
          if (this.isListening && !this.isRecording) {
            console.log('Retrying recording after error...');
            await this.startRecording();
          }
        }, 5000); // 5 second delay before retry
      }
    }
  }

  stopRecording() {
    if (this.mediaRecorder && this.isRecording) {
      this.mediaRecorder.stop();
      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());
      this.isRecording = false;

      this.updateUI('processing');
      this.updateStatus('Processing your message...');
    }
  }

  async processRecording() {
    try {
      // Check if we have audio data
      if (this.audioChunks.length === 0) {
        throw new Error('No audio data recorded');
      }

      // Determine the correct MIME type based on what was recorded
      let mimeType = this.recordedMimeType || 'audio/wav';
      if (this.mediaRecorder && this.mediaRecorder.mimeType) {
        mimeType = this.mediaRecorder.mimeType;
      }

      console.log('Processing audio with MIME type:', mimeType);
      console.log('Audio chunks:', this.audioChunks.length);

      const audioBlob = new Blob(this.audioChunks, { type: mimeType });

      // Check blob size
      if (audioBlob.size === 0) {
        throw new Error('Audio blob is empty');
      }

      console.log('Audio blob size:', audioBlob.size, 'bytes');

      // Send to speech-to-text API
      const transcription = await this.speechToText(audioBlob);

      if (transcription) {
        this.addMessage(transcription, 'user');

        // Send to AI for response
        const aiResponse = await this.getAIResponse(transcription);

        if (aiResponse) {
          this.addMessage(aiResponse, 'ai');

          // Convert AI response to speech
          await this.textToSpeech(aiResponse);

          // Extract lead information
          this.extractLeadInfo(transcription, aiResponse);
        }
      }

      // If still in listening mode, restart recording after a brief pause
      if (this.isListening) {
        this.updateUI('listening');
        this.updateStatus('Voice mode ON - Listening for speech...');

        // Wait a moment before restarting recording to allow for AI response
        this.voiceActivationTimeout = setTimeout(async () => {
          if (this.isListening && !this.isRecording) {
            await this.startRecording();
          }
        }, 2000); // 2 second delay
      } else {
        this.updateUI('ready');
        this.updateStatus('Ready for next message');
      }
    } catch (error) {
      console.error('Processing error:', error);
      this.showError(
        'Processing Failed',
        'Could not process your voice message. Please try again.',
        `Error: ${error.message}\nCheck your microphone and internet connection.`
      );

      // If still in listening mode, try to restart recording
      if (this.isListening) {
        this.updateUI('listening');
        this.voiceActivationTimeout = setTimeout(async () => {
          if (this.isListening && !this.isRecording) {
            await this.startRecording();
          }
        }, 3000); // 3 second delay after error
      } else {
        this.updateUI('ready');
      }
    }
  }

  async speechToText(audioBlob) {
    try {
      // Update animation to show speech processing
      this.showAIThinking('Processing speech...');

      const formData = new FormData();

      // Determine file extension based on MIME type
      let filename = 'recording.wav';
      let mimeType = audioBlob.type;

      console.log('Audio blob MIME type:', mimeType);

      if (mimeType.includes('webm')) {
        filename = 'recording.webm';
      } else if (mimeType.includes('mp4')) {
        filename = 'recording.mp4';
      } else if (mimeType.includes('ogg')) {
        filename = 'recording.ogg';
      } else if (mimeType.includes('mpeg')) {
        filename = 'recording.mp3';
      } else {
        // Default to wav for unknown types
        filename = 'recording.wav';
      }

      console.log('Sending audio file:', filename, 'Size:', audioBlob.size);
      formData.append('audio', audioBlob, filename);

      const response = await this.authService.makeAuthenticatedRequest(
        '/api/speech-to-text',
        {
          method: 'POST',
          body: formData,
          // Don't set headers for FormData - let browser handle Content-Type with boundary
        }
      );

      console.log('Speech-to-text response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Speech-to-text HTTP error:', errorText);
        throw new Error(
          `HTTP error! status: ${response.status} - ${errorText}`
        );
      }

      const result = await response.json();
      console.log('Speech-to-text result:', result);

      if (result.success && result.transcription) {
        console.log('Transcription successful:', result.transcription);
        return result.transcription;
      } else {
        throw new Error(
          result.error || result.details || 'Failed to transcribe audio'
        );
      }
    } catch (error) {
      console.error('Speech-to-text error:', error);
      this.showError(
        'Speech Recognition Failed',
        'Could not convert your speech to text. Please try again.',
        `Error: ${error.message}\nThis might be due to audio format issues or network problems.`
      );
      return null;
    } finally {
      // Don't hide animation here - let it continue to AI response
    }
  }

  async sendTextMessage() {
    const message = this.textInput.value.trim();
    if (!message) return;

    // Mark that user has interacted with the page
    this.userHasInteracted = true;

    // Create audio context for auto-play (must be done in user interaction)
    this.prepareAudioContext();

    // Play pending welcome audio if available
    this.playPendingWelcomeAudio();

    // Pre-create blessed audio element for TTS response during user interaction
    this.prepareAudioForTTS();

    // Clear the input
    this.textInput.value = '';

    // Disable send button temporarily
    this.sendButton.disabled = true;

    try {
      // Add user message to conversation
      this.addMessage(message, 'user');

      // Get AI response
      const aiResponse = await this.getAIResponse(message);

      if (aiResponse) {
        // Add AI response to conversation
        this.addMessage(aiResponse, 'ai');

        // Always play TTS response for text input (users expect voice response)
        await this.textToSpeech(aiResponse);
      }
    } catch (error) {
      console.error('Error sending text message:', error);
      this.showError(
        'Message Failed',
        'Could not send your message. Please try again.',
        `Error: ${error.message}`
      );
    } finally {
      // Re-enable send button
      this.sendButton.disabled = false;
    }
  }

  async getAIResponse(message) {
    try {
      // Update animation to show AI thinking
      this.showAIThinking('Sarah is thinking...');

      const response = await this.authService.makeAuthenticatedRequest(
        '/api/chat',
        {
          method: 'POST',
          body: JSON.stringify({
            message,
            context: 'stock_business_lead',
            leadData: this.leadData,
            conversationHistory: this.conversationHistory,
            sessionId: this.sessionId,
          }),
        }
      );

      if (!response.ok) {
        console.error('AI response error:', response);
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success && result.response) {
        // Add to conversation history
        this.conversationHistory.push(
          { role: 'user', content: message },
          { role: 'assistant', content: result.response }
        );

        // Keep conversation history manageable (last 10 exchanges)
        if (this.conversationHistory.length > 20) {
          this.conversationHistory = this.conversationHistory.slice(-20);
        }

        // Store attribution data for enhanced display
        this.lastResponseAttributions = result.attributions || [];

        // Clean markdown formatting from AI response
        const cleanResponse = this.stripMarkdownFormatting(result.response);
        return cleanResponse;
      } else {
        throw new Error(result.error || 'Failed to get AI response');
      }
    } catch (error) {
      console.error('AI response error:', error);
      this.showError(
        'AI Response Failed',
        'Could not get a response from the AI. Please try again.',
        `Error: ${error.message}\nThis might be due to API issues or network problems.`
      );
      return 'Sorry, I encountered an error. Please try again.';
    } finally {
      // Don't hide animation here - let it continue to speech
    }
  }

  /**
   * Strip markdown formatting from text
   * @param {string} text - Text with potential markdown formatting
   * @returns {string} Clean text without markdown
   */
  stripMarkdownFormatting(text) {
    if (!text || typeof text !== 'string') {
      return text;
    }

    return (
      text
        // Remove bold formatting **text** and __text__
        .replace(/\*\*(.*?)\*\*/g, '$1')
        .replace(/__(.*?)__/g, '$1')
        // Remove italic formatting *text* and _text_
        .replace(/\*(.*?)\*/g, '$1')
        .replace(/_(.*?)_/g, '$1')
        // Remove strikethrough ~~text~~
        .replace(/~~(.*?)~~/g, '$1')
        // Remove inline code `text`
        .replace(/`(.*?)`/g, '$1')
        // Remove headers ### text
        .replace(/^#{1,6}\s+/gm, '')
        // Remove list markers - and *
        .replace(/^[\s]*[-*+]\s+/gm, '')
        // Remove numbered list markers
        .replace(/^[\s]*\d+\.\s+/gm, '')
        // Clean up any remaining multiple spaces
        .replace(/\s+/g, ' ')
        .trim()
    );
  }

  async textToSpeech(text) {
    console.log('🎵 textToSpeech() called with text:', text.substring(0, 50) + '...');
    try {
      // Update status to show speech generation
      this.updateStatus('Generating speech...', false);

      const response = await this.authService.makeAuthenticatedRequest(
        '/api/text-to-speech',
        {
          method: 'POST',
          body: JSON.stringify({ text }),
        }
      );

      console.log('🎵 TTS API response status:', response.status, response.statusText);

      if (response.ok) {
        const audioBlob = await response.blob();
        const audioUrl = URL.createObjectURL(audioBlob);

        this.currentAudio = audioUrl;

        // Use the pre-blessed audio element created during user interaction
        let audioElement;
        if (this.ttsAudioElement) {
          // Use the blessed audio element
          audioElement = this.ttsAudioElement;
          audioElement.src = audioUrl;
          console.log('🎵 Using blessed TTS audio element');
        } else {
          // Fallback: create new element (may not work on mobile)
          audioElement = new Audio();
          audioElement.src = audioUrl;
          audioElement.preload = 'auto';
          console.log('⚠️ Using new audio element (may fail on mobile)');
        }

        // Store references
        this.currentAudio = audioUrl;
        this.audioPlayer = audioElement;
        this.playButton.disabled = false;

        // Set up event handlers for this audio element
        audioElement.addEventListener('ended', () => {
          console.log('🎵 Audio playback ended');

          // Hide stock preview when audio ends
          this.hideStockPreview();

          if (this.isListening) {
            // If in listening mode, restart recording after AI finishes speaking
            this.updateUI('listening');
            this.showAIThinking('Listening...');
            this.voiceActivationTimeout = setTimeout(async () => {
              if (this.isListening && !this.isRecording) {
                await this.startRecording();
              }
            }, 1000);
          } else {
            this.hideAIThinking();
            this.updateUI('ready');
          }
        });

        audioElement.addEventListener('error', (e) => {
          console.error('❌ Audio playback error:', e);
          this.handleAutoPlayBlocked();
        });

        audioElement.addEventListener('loadstart', () => {
          console.log('🎵 Audio loading started');
        });

        audioElement.addEventListener('canplay', () => {
          console.log('🎵 Audio can start playing');
        });

        // Force auto-play - this should work since user just interacted
        try {
          this.updateUI('speaking');
          this.showAIThinking('Sarah is speaking...');

          console.log('🎵 Attempting auto-play...');

          // Try multiple approaches for mobile compatibility
          const attemptPlay = async () => {
            console.log(
              '🎵 Audio element type:',
              audioElement === this.ttsAudioElement
                ? 'blessed TTS element'
                : 'new element'
            );
            console.log('🎵 User has interacted:', this.userHasInteracted);
            console.log(
              '🎵 Audio context unlocked:',
              this.audioContextUnlocked
            );

            try {
              // First attempt: direct play
              await audioElement.play();
              console.log('✅ Audio auto-play successful!');
              return true;
            } catch (error) {
              console.log('❌ Direct play failed:', error.name, error.message);

              try {
                // Second attempt: muted start then unmute
                console.log('🔄 Trying muted start technique...');
                audioElement.muted = true;
                await audioElement.play();
                audioElement.muted = false;
                console.log('✅ Audio playing with muted start technique');
                return true;
              } catch (mutedError) {
                console.log(
                  '❌ Muted start failed:',
                  mutedError.name,
                  mutedError.message
                );

                try {
                  // Third attempt: set volume low then restore
                  console.log('🔄 Trying low volume technique...');
                  const originalVolume = audioElement.volume;
                  audioElement.volume = 0.01;
                  await audioElement.play();
                  audioElement.volume = originalVolume;
                  console.log('✅ Audio playing with low volume technique');
                  return true;
                } catch (volumeError) {
                  console.log(
                    '❌ All autoplay attempts failed:',
                    volumeError.name,
                    volumeError.message
                  );
                  this.handleAutoPlayBlocked();
                  return false;
                }
              }
            }
          };

          attemptPlay();
        } catch (error) {
          console.log('❌ Audio setup error:', error.message);
          this.handleAutoPlayBlocked();
        }
      } else {
        console.log('❌ TTS API failed with status:', response.status);
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    } catch (error) {
      console.error('❌ Text-to-speech error:', error);
      this.showError(
        'Speech Generation Failed',
        'Could not convert the AI response to speech, but you can still read the text.',
        `Error: ${error.message}\nThe conversation can continue without audio.`
      );
    }
  }

  addMessage(text, sender) {
    console.log('📝 addMessage called:', { sender, textLength: text.length, textPreview: text.substring(0, 50) + '...' });

    // Remove welcome message if it exists
    const welcomeMessage =
      this.conversationDisplay.querySelector('.welcome-message');
    if (welcomeMessage) {
      welcomeMessage.remove();
    }

    // Only hide predefined prompts when user sends a message (not for automatic AI welcome)
    if (sender === 'user') {
      this.hidePredefinedPrompts();
    }

    // Create enhanced message display for AI responses with attribution
    if (sender === 'ai' && this.lastResponseAttributions && this.lastResponseAttributions.length > 0) {
      this.addEnhancedAIMessage(text, this.lastResponseAttributions);
    } else {
      // Standard message display
      const messageDiv = document.createElement('div');
      messageDiv.className = `message ${sender}-message`;
      messageDiv.textContent = text;
      this.conversationDisplay.appendChild(messageDiv);

      // If this is an AI message, check for stocks and add inline cards
      if (sender === 'ai') {
        this.addInlineStockCards(text, messageDiv);
      }
    }

    // Clear attribution data after use
    if (sender === 'ai') {
      this.lastResponseAttributions = null;
    }

    // Auto-scroll to bottom for new messages
    this.scrollToBottom();
  }

  addEnhancedAIMessage(text, attributions) {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message ai-message enhanced-ai-message';

    // Split text into sentences for attribution matching
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 10);
    let enhancedHTML = '';

    sentences.forEach((sentence, index) => {
      const trimmedSentence = sentence.trim();
      if (!trimmedSentence) return;

      // Check if this sentence has attribution
      const attribution = attributions.find(attr => attr.sentenceIndex === index);

      if (attribution) {
        // Create attributed content with community indicator
        enhancedHTML += `
          <div class="attributed-content">
            <div class="sentence-with-attribution">
              ${this.escapeHtml(trimmedSentence)}.
              <span class="community-indicator" title="Information from community discussion">
                <i class="fas fa-users"></i>
              </span>
            </div>
            <div class="attribution-details">
              <div class="attribution-header">
                <span class="attribution-source">From ${attribution.source}</span>
                <span class="attribution-confidence">${Math.round(attribution.confidence)}% match</span>
              </div>
              <div class="attribution-meta">
                <span class="attribution-user">
                  <i class="fas fa-user"></i> ${attribution.user}
                </span>
                <span class="attribution-time">
                  <i class="fas fa-clock"></i> ${attribution.timestamp}
                </span>
              </div>
              <div class="original-content">
                <strong>Original:</strong> "${this.escapeHtml(attribution.originalContent)}"
              </div>
            </div>
          </div>
        `;
      } else {
        // Regular sentence without attribution
        enhancedHTML += `<span class="regular-content">${this.escapeHtml(trimmedSentence)}.</span> `;
      }
    });

    messageDiv.innerHTML = `<strong>Sarah:</strong> <div class="enhanced-response-content">${enhancedHTML}</div>`;
    this.conversationDisplay.appendChild(messageDiv);

    // Add click handlers for attribution details
    this.setupAttributionInteractions(messageDiv);

    // Check for stocks and add inline cards
    this.addInlineStockCards(text, messageDiv);
  }

  setupAttributionInteractions(messageDiv) {
    const attributedContents = messageDiv.querySelectorAll('.attributed-content');

    attributedContents.forEach(content => {
      const details = content.querySelector('.attribution-details');
      const indicator = content.querySelector('.community-indicator');

      // Initially hide details
      details.style.display = 'none';

      // Toggle details on click
      indicator.addEventListener('click', (e) => {
        e.stopPropagation();
        const isVisible = details.style.display !== 'none';
        details.style.display = isVisible ? 'none' : 'block';

        // Update indicator appearance
        indicator.classList.toggle('active', !isVisible);
      });

      // Close details when clicking outside
      document.addEventListener('click', (e) => {
        if (!content.contains(e.target)) {
          details.style.display = 'none';
          indicator.classList.remove('active');
        }
      });
    });
  }

  async addInlineStockCards(text, messageDiv) {
    try {
      // Detect stocks in the AI response
      const detectedStocks = await this.detectStocksInResponse(text);

      if (detectedStocks.length > 0) {
        console.log(
          `📊 Detected ${detectedStocks.length} stock(s) in AI response:`,
          detectedStocks.map((s) => s.ticker).join(', ')
        );

        // Always create carousel for consistency, even with single stock
        const carousel = this.createStockCarousel(detectedStocks);
        messageDiv.appendChild(carousel);

        // Scroll after dynamic content is appended
        this.scrollToBottom();
        // Scroll again on next frame to account for image loads/layout
        requestAnimationFrame(() => this.scrollToBottom());
        setTimeout(() => this.scrollToBottom(), 100);
      }
    } catch (error) {
      console.error('Error adding inline stock cards:', error);
    }
  }

  createInlineStockCard(stockData) {
    const cardDiv = document.createElement('div');
    cardDiv.className = 'inline-stock-card';

    // Generate community member data
    const communityMember = this.generateCommunityMember();
    const investmentThesis = this.generateInvestmentThesis(stockData.ticker);

    cardDiv.innerHTML = `
      <div class="flip-card-inner">
        <div class="flip-card-front">
          <div class="inline-stock-content">
            <div class="stock-header">
              <span class="company-name">${stockData.companyName}</span>
              <span class="ticker-symbol">${stockData.ticker}</span>
            </div>

            <div class="community-insight">
              <div class="profile-section">
                <div class="profile-avatar">
                  <img src="${communityMember.avatar}" alt="${
      communityMember.name
    }" />
                </div>
                <span class="member-name">${communityMember.name}</span>
              </div>
            </div>

            <div class="compact-metrics">
              <div class="labels-row">
                <div class="metric-item price-item"><span class="metric-label">Price</span></div>
                <div class="metric-item cap-item"><span class="metric-label">Market Cap</span></div>
                <div class="metric-item target-item"><span class="metric-label">Price Target</span></div>
              </div>
              <div class="values-row">
                <div class="metric-item price-item">
                  <div class="price-info">
                    <span class="current-price">$${stockData.price}</span>
                    <span class="price-change ${
                      stockData.change >= 0 ? 'positive' : 'negative'
                    }">
                      ${stockData.change >= 0 ? '+' : ''}${
      stockData.changePercent
    }%
                    </span>
                  </div>
                </div>
                <div class="metric-item cap-item">
                  <span class="metric-value">${stockData.marketCap}</span>
                </div>
                <div class="metric-item target-item">
                  <span class="metric-value">$${
                    stockData.priceTarget
                  } <span class="target-upside">+60%</span></span>
                </div>
              </div>
              <div class="timestamp">
                <span class="pricing-note">Pricing delayed 20 minutes. ${new Date().toLocaleDateString(
                  'en-US',
                  {
                    month: 'short',
                    day: 'numeric',
                    year: 'numeric',
                  }
                )} ${new Date().toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    })}</span>
              </div>
              <button class="flip-toggle-button" aria-pressed="false" aria-label="Flip card"><i class="fas fa-info-circle"></i>&nbsp;View Thesis</button>
            </div>
          </div>
        </div>
        <div class="flip-card-back">
          <div class="inline-stock-content">
          
            <div class="stock-header">
              <span class="company-name">${stockData.companyName}</span>
              <span class="ticker-symbol">${stockData.ticker}</span>
            </div>

            <div class="thesis-section">
              <span class="thesis-label">Thesis</span>
              <p class="thesis-text">${investmentThesis}</p>
            </div>

<button class="flip-toggle-button" aria-pressed="false" aria-label="Flip card"><i class="fas fa-undo-alt"></i></button>
          </div>
        </div>
      </div>
    `;

    // Enable flip mode based on setting
    if (this.flipCardsEnabled) {
      cardDiv.classList.add('flip-card');
    }

    // Helper to pause/resume carousel auto-advance when flipping
    const updateCarouselAutoAdvance = () => {
      const container = cardDiv.closest('.stock-carousel-container');
      if (!container) return;
      const intervalId = container.getAttribute('data-auto-advance-id');
      if (cardDiv.classList.contains('flipped')) {
        if (intervalId) {
          clearInterval(parseInt(intervalId));
          container.removeAttribute('data-auto-advance-id');
        }
      } else {
        // resume auto-advance
        this.startCarouselAutoAdvance(container);
      }
    };

    // Flip buttons listeners (explicit control + keyboard)
    const flipButtons = cardDiv.querySelectorAll('.flip-toggle-button');
    flipButtons.forEach((btn) => {
      btn.setAttribute('tabindex', '0');
      btn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        if (!this.flipCardsEnabled) return;
        const nowFlipped = cardDiv.classList.toggle('flipped');
        flipButtons.forEach((b) =>
          b.setAttribute('aria-pressed', String(nowFlipped))
        );
        updateCarouselAutoAdvance();
      });
      btn.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          e.stopPropagation();
          if (!this.flipCardsEnabled) return;
          const nowFlipped = cardDiv.classList.toggle('flipped');
          flipButtons.forEach((b) =>
            b.setAttribute('aria-pressed', String(nowFlipped))
          );
          updateCarouselAutoAdvance();
        }
      });
    });

    // Set inner height to the taller face to avoid layout jumps
    requestAnimationFrame(() => {
      const inner = cardDiv.querySelector('.flip-card-inner');
      const front = cardDiv.querySelector('.flip-card-front');
      const back = cardDiv.querySelector('.flip-card-back');
      if (inner && front && back) {
        const h = Math.max(front.offsetHeight, back.offsetHeight);
        inner.style.height = h + 'px';
      }
    });

    return cardDiv;
  }

  createStockCarousel(stocksArray) {
    const carouselContainer = document.createElement('div');
    carouselContainer.className = 'stock-carousel-container';

    // Create carousel wrapper
    const carouselWrapper = document.createElement('div');
    carouselWrapper.className = 'stock-carousel-wrapper';

    // Create carousel track
    const carouselTrack = document.createElement('div');
    carouselTrack.className = 'stock-carousel-track';

    // Add stock cards to track
    stocksArray.forEach((stock, index) => {
      const stockCard = this.createInlineStockCard(stock);
      stockCard.classList.add('carousel-card');
      stockCard.setAttribute('data-index', index);
      carouselTrack.appendChild(stockCard);
    });

    // Create dot indicators
    const dotsContainer = document.createElement('div');
    dotsContainer.className = 'carousel-dots';

    stocksArray.forEach((_, index) => {
      const dot = document.createElement('button');
      dot.className = `carousel-dot ${index === 0 ? 'active' : ''}`;
      dot.setAttribute('data-index', index);
      dot.addEventListener('click', () =>
        this.goToSlide(carouselContainer, index)
      );
      dotsContainer.appendChild(dot);
    });

    // Assemble carousel
    carouselWrapper.appendChild(carouselTrack);
    carouselContainer.appendChild(carouselWrapper);
    carouselContainer.appendChild(dotsContainer);

    // Initialize carousel state
    carouselContainer.setAttribute('data-current-slide', '0');
    carouselContainer.setAttribute('data-total-slides', stocksArray.length);

    // Add touch/swipe support
    this.addCarouselTouchSupport(carouselContainer);

    // Start auto-advance
    this.startCarouselAutoAdvance(carouselContainer);

    return carouselContainer;
  }

  generateCommunityMember() {
    const members = [
      {
        name: 'Alex Chen',
        avatar:
          'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face&auto=format',
      },
      {
        name: 'Sarah Kim',
        avatar:
          'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face&auto=format',
      },
      {
        name: 'Marcus Johnson',
        avatar:
          'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face&auto=format',
      },
      {
        name: 'Emma Rodriguez',
        avatar:
          'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face&auto=format',
      },
      {
        name: 'David Park',
        avatar:
          'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=face&auto=format',
      },
    ];

    return members[Math.floor(Math.random() * members.length)];
  }

  generateInvestmentThesis(ticker) {
    const theses = {
      'AAPL': [
        'Strong services revenue growth with expanding ecosystem lock-in.',
        'AI integration across devices driving upgrade cycles.',
        'Vision Pro represents next major platform opportunity.',
      ],
      'TSLA': [
        'FSD breakthrough could unlock massive autonomous value.',
        'Energy storage business scaling rapidly with grid demand.',
        'Manufacturing efficiency improvements driving margin expansion.',
      ],
      'MSFT': [
        'Azure cloud growth accelerating with AI workloads.',
        'Copilot integration creating new revenue streams.',
        'Enterprise software moat strengthening with AI capabilities.',
      ],
      'GOOGL': [
        'Search dominance enhanced by AI integration.',
        'Cloud infrastructure gaining share in enterprise market.',
        "YouTube's creator economy driving engagement growth.",
      ],
      'AMZN': [
        'AWS margins expanding as scale advantages compound.',
        'Advertising business becoming major profit driver.',
        'Logistics network creating competitive moats.',
      ],
      'NVDA': [
        'AI chip demand exceeding supply for foreseeable future.',
        'Data center transformation driving multi-year growth.',
        'Software ecosystem creating recurring revenue streams.',
      ],
      'META': [
        'Metaverse investments positioning for next computing platform.',
        'AI-driven ad targeting improving monetization efficiency.',
        'Reality Labs breakthrough could unlock new markets.',
      ],
    };

    const tickerTheses = theses[ticker] || [
      'Strong fundamentals with compelling growth prospects.',
      'Market leadership position in expanding sector.',
      'Innovative product pipeline driving future growth.',
    ];

    return tickerTheses[Math.floor(Math.random() * tickerTheses.length)];
  }

  goToSlide(carouselContainer, slideIndex) {
    const track = carouselContainer.querySelector('.stock-carousel-track');
    const dots = carouselContainer.querySelectorAll('.carousel-dot');
    const totalSlides = parseInt(
      carouselContainer.getAttribute('data-total-slides')
    );

    // Ensure slideIndex is within bounds
    slideIndex = Math.max(0, Math.min(slideIndex, totalSlides - 1));

    // Update track position
    const translateX = -slideIndex * 100;
    track.style.transform = `translateX(${translateX}%)`;

    // Update dots
    dots.forEach((dot, index) => {
      dot.classList.toggle('active', index === slideIndex);
    });

    // Update current slide
    carouselContainer.setAttribute('data-current-slide', slideIndex);

    // Reset auto-advance timer
    this.resetCarouselAutoAdvance(carouselContainer);
  }

  addCarouselTouchSupport(carouselContainer) {
    const track = carouselContainer.querySelector('.stock-carousel-track');
    let startX = 0;
    let currentX = 0;
    let isDragging = false;

    // Touch events
    track.addEventListener('touchstart', (e) => {
      if (e.target.closest('.flip-toggle-button')) return; // don't start drag on flip button
      startX = e.touches[0].clientX;
      isDragging = true;
      track.style.transition = 'none';
    });

    track.addEventListener('touchmove', (e) => {
      if (!isDragging) return;
      e.preventDefault();
      currentX = e.touches[0].clientX;
      const diffX = currentX - startX;
      const currentSlide = parseInt(
        carouselContainer.getAttribute('data-current-slide')
      );
      const translateX =
        -currentSlide * 100 + (diffX / track.offsetWidth) * 100;
      track.style.transform = `translateX(${translateX}%)`;
    });

    track.addEventListener('touchend', () => {
      if (!isDragging) return;
      isDragging = false;
      track.style.transition = 'transform 0.3s ease';

      const diffX = currentX - startX;
      const threshold = 50; // Minimum swipe distance
      const currentSlide = parseInt(
        carouselContainer.getAttribute('data-current-slide')
      );

      if (Math.abs(diffX) > threshold) {
        if (diffX > 0) {
          // Swipe right - go to previous slide
          this.goToSlide(carouselContainer, currentSlide - 1);
        } else {
          // Swipe left - go to next slide
          this.goToSlide(carouselContainer, currentSlide + 1);
        }
      } else {
        // Snap back to current slide
        this.goToSlide(carouselContainer, currentSlide);
      }
    });

    // Mouse events for desktop
    track.addEventListener('mousedown', (e) => {
      if (e.target.closest('.flip-toggle-button')) return; // don't start drag on flip button
      startX = e.clientX;
      isDragging = true;
      track.style.transition = 'none';
      e.preventDefault();
    });

    document.addEventListener('mousemove', (e) => {
      if (!isDragging) return;
      currentX = e.clientX;
      const diffX = currentX - startX;
      const currentSlide = parseInt(
        carouselContainer.getAttribute('data-current-slide')
      );
      const translateX =
        -currentSlide * 100 + (diffX / track.offsetWidth) * 100;
      track.style.transform = `translateX(${translateX}%)`;
    });

    document.addEventListener('mouseup', () => {
      if (!isDragging) return;
      isDragging = false;
      track.style.transition = 'transform 0.3s ease';

      const diffX = currentX - startX;
      const threshold = 50;
      const currentSlide = parseInt(
        carouselContainer.getAttribute('data-current-slide')
      );

      if (Math.abs(diffX) > threshold) {
        if (diffX > 0) {
          this.goToSlide(carouselContainer, currentSlide - 1);
        } else {
          this.goToSlide(carouselContainer, currentSlide + 1);
        }
      } else {
        this.goToSlide(carouselContainer, currentSlide);
      }
    });
  }

  startCarouselAutoAdvance(carouselContainer) {
    const totalSlides = parseInt(
      carouselContainer.getAttribute('data-total-slides')
    );

    // Don't auto-advance if there's only one slide
    if (totalSlides <= 1) return;

    const autoAdvanceInterval = setInterval(() => {
      const currentSlide = parseInt(
        carouselContainer.getAttribute('data-current-slide')
      );
      const nextSlide = (currentSlide + 1) % totalSlides;
      this.goToSlide(carouselContainer, nextSlide);
    }, 4000); // 4 seconds

    // Store interval ID for cleanup
    carouselContainer.setAttribute('data-auto-advance-id', autoAdvanceInterval);
  }

  resetCarouselAutoAdvance(carouselContainer) {
    // Clear existing interval
    const intervalId = carouselContainer.getAttribute('data-auto-advance-id');
    if (intervalId) {
      clearInterval(parseInt(intervalId));
    }

    // Start new interval
    this.startCarouselAutoAdvance(carouselContainer);
  }

  extractLeadInfo(userMessage, aiResponse) {
    // Simple lead information extraction
    // In a real implementation, this would be more sophisticated
    // aiResponse could be used for more advanced extraction in the future

    const nameMatch =
      userMessage.match(/my name is (\w+)/i) || userMessage.match(/i'm (\w+)/i);
    if (nameMatch) {
      this.leadData.name = nameMatch[1];
    }

    const emailMatch = userMessage.match(/[\w.-]+@[\w.-]+\.\w+/);
    if (emailMatch) {
      this.leadData.email = emailMatch[0];
    }

    const phoneMatch = userMessage.match(/(\d{3}[-.]?\d{3}[-.]?\d{4})/);
    if (phoneMatch) {
      this.leadData.phone = phoneMatch[1];
    }

    // Update lead display
    this.updateLeadDisplay();
  }

  updateLeadDisplay() {
    if (Object.keys(this.leadData).length > 0) {
      let leadHtml = '<div class="lead-details">';

      if (this.leadData.name) {
        leadHtml += `<p><strong>Name:</strong> ${this.leadData.name}</p>`;
      }
      if (this.leadData.email) {
        leadHtml += `<p><strong>Email:</strong> ${this.leadData.email}</p>`;
      }
      if (this.leadData.phone) {
        leadHtml += `<p><strong>Phone:</strong> ${this.leadData.phone}</p>`;
      }

      leadHtml += '</div>';
      this.leadInfo.innerHTML = leadHtml;

      this.saveLeadBtn.disabled = false;
      this.exportLeadBtn.disabled = false;
    }
  }

  updateUI(state) {
    // Check if critical elements exist before updating UI
    if (!this.micButton || !this.micIcon) {
      console.error('Critical UI elements not found, cannot update UI');
      return;
    }

    switch (state) {
      case 'listening':
        this.micButton.classList.add('listening');
        this.micButton.classList.remove('recording');
        this.micIcon.className = 'fas fa-microphone-slash';
        if (this.micStatus) {
          this.micStatus.textContent = 'Voice ON - Click to turn OFF';
        }

        this.hideAISpeaking(); // Hide blob animation
        break;
      case 'recording':
        this.micButton.classList.add('recording');
        this.micButton.classList.remove('listening');
        this.micIcon.className = 'fas fa-stop';
        if (this.micStatus) {
          this.micStatus.textContent = 'Recording...';
        }

        this.hideAISpeaking(); // Hide blob animation
        break;
      case 'processing':
        this.micButton.classList.remove('recording', 'listening');
        this.micIcon.className = 'fas fa-spinner fa-spin';
        if (this.micStatus) {
          this.micStatus.textContent = 'Processing...';
        }

        this.hideAISpeaking(); // Hide blob animation
        break;
      case 'speaking':
        this.micButton.classList.remove('recording');
        if (this.isListening) {
          this.micButton.classList.add('listening');
          this.micIcon.className = 'fas fa-microphone-slash';
          if (this.micStatus) {
            this.micStatus.textContent = 'Voice ON - AI speaking...';
          }
        } else {
          this.micIcon.className = 'fas fa-microphone';
          if (this.micStatus) {
            this.micStatus.textContent = 'AI is speaking...';
          }
        }

        this.showAISpeaking(); // Use blob animation for speaking
        break;
      case 'ready':
        this.micButton.classList.remove('recording', 'listening');
        this.micIcon.className = 'fas fa-microphone';
        if (this.micStatus) {
          this.micStatus.textContent = 'Click to turn voice ON';
        }

        this.hideAISpeaking(); // Hide blob animation
        break;
    }
  }

  updateStatus(message, isError = false) {
    const statusText = this.statusIndicator.querySelector('.status-text');
    statusText.textContent = message;

    // Clear any existing hide timer
    if (this.statusHideTimer) {
      clearTimeout(this.statusHideTimer);
    }

    // Show the status indicator
    this.statusIndicator.classList.remove('hidden');

    // Add error styling if it's an error
    if (isError) {
      this.statusIndicator.style.background = 'rgba(220, 53, 69, 0.9)';
      statusText.style.color = '#ffffff';

      // Hide error status after 3 seconds
      this.statusHideTimer = setTimeout(() => {
        this.statusIndicator.classList.add('hidden');
      }, 3000);
    } else {
      this.statusIndicator.style.background = 'rgba(0, 0, 0, 0.7)';
      statusText.style.color = '#00d4aa';

      // Hide normal status after 2 seconds
      this.statusHideTimer = setTimeout(() => {
        this.statusIndicator.classList.add('hidden');
      }, 2000);
    }
  }

  showError(title, message, details = null) {
    // Create error message in conversation
    const errorDiv = document.createElement('div');
    errorDiv.className = 'message error-message';
    errorDiv.innerHTML = `
            <div class="error-content">
                <strong>❌ ${title}</strong>
                <p>${message}</p>
                ${
                  details
                    ? `<details><summary>Technical Details</summary><pre>${details}</pre></details>`
                    : ''
                }
            </div>
        `;

    // Remove welcome message if it exists
    const welcomeMessage =
      this.conversationDisplay.querySelector('.welcome-message');
    if (welcomeMessage) {
      welcomeMessage.remove();
    }

    this.conversationDisplay.appendChild(errorDiv);
    this.scrollToBottom();

    // Also update status
    this.updateStatus(`Error: ${title}`, true);
  }

  handleAutoPlayBlocked() {
    // For mobile devices, try alternative approaches instead of showing play button
    console.log(
      '🔊 Auto-play blocked, attempting alternative playback methods...'
    );

    // Try playing with the blessed audio element if available
    const audioToUse = this.ttsAudioElement || this.audioPlayer;

    if (audioToUse && this.currentAudio) {
      // Ensure the audio source is set
      if (audioToUse !== this.audioPlayer) {
        audioToUse.src = this.currentAudio;
      }

      // Attempt immediate retry with different approach
      setTimeout(() => {
        audioToUse.muted = true;
        audioToUse
          .play()
          .then(() => {
            // Unmute after starting
            audioToUse.muted = false;
            console.log('✅ Audio playing with muted start technique');
            this.updateUI('speaking');
            this.showAIThinking('Sarah is speaking...');
          })
          .catch(() => {
            // If all else fails, just continue without audio but don't show play button
            console.log(
              '❌ All autoplay attempts failed - continuing without audio'
            );
            this.updateStatus('Audio unavailable on this device', false);
            this.updateUI('ready');
          });
      }, 100);
    } else {
      this.updateStatus('Audio unavailable', false);
      this.updateUI('ready');
    }
  }

  playLastResponse() {
    if (this.currentAudio) {
      // Show speaking animation when manually playing
      this.showAIThinking('Sarah is speaking...');
      this.audioPlayer.play();
    }
  }

  stopAudio() {
    this.audioPlayer.pause();
    this.audioPlayer.currentTime = 0;
    this.oldStopButton.disabled = true;
  }

  stopEverything() {
    // Stop voice mode if active
    if (this.isListening) {
      this.stopVoiceMode();
    }

    // Stop recording if active
    if (this.isRecording) {
      this.stopRecording();
    }

    // Stop audio playback
    this.audioPlayer.pause();
    this.audioPlayer.currentTime = 0;

    // Stop all carousel auto-advance timers
    this.stopAllCarouselTimers();

    // Reset UI to ready state
    this.updateUI('ready');
    this.updateStatus('Stopped');
  }

  stopAllCarouselTimers() {
    // Find all carousel containers and stop their timers
    const carousels = document.querySelectorAll('.stock-carousel-container');
    carousels.forEach((carousel) => {
      const intervalId = carousel.getAttribute('data-auto-advance-id');
      if (intervalId) {
        clearInterval(parseInt(intervalId));
        carousel.removeAttribute('data-auto-advance-id');
      }
    });
  }

  async saveLead() {
    try {
      const response = await fetch('/api/save-lead', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(this.leadData),
      });

      if (response.ok) {
        this.updateStatus('Lead saved successfully');
      } else {
        this.updateStatus('Error saving lead');
      }
    } catch (error) {
      console.error('Save lead error:', error);
      this.updateStatus('Error saving lead');
    }
  }

  exportLead() {
    const leadText = JSON.stringify(this.leadData, null, 2);
    const blob = new Blob([leadText], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = `lead_${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  // AI Animation Methods
  showAIThinking(message = 'Sarah is thinking...') {
    this.aiThinkingIndicator.style.display = 'flex';
    // Update the text message
    const thinkingText =
      this.aiThinkingIndicator.querySelector('.thinking-text');
    if (thinkingText) {
      thinkingText.textContent = message;
    }
    // Set state for styling
    if (message.includes('speaking')) {
      this.aiThinkingIndicator.setAttribute('data-state', 'speaking');
    } else {
      this.aiThinkingIndicator.setAttribute('data-state', 'thinking');
    }
  }

  hideAIThinking() {
    this.aiThinkingIndicator.style.display = 'none';
    // Reset to default message
    const thinkingText =
      this.aiThinkingIndicator.querySelector('.thinking-text');
    if (thinkingText) {
      thinkingText.textContent = 'Sarah is thinking...';
    }
  }

  showAISpeaking() {
    // Use the thinking animation for speaking as well
    this.showAIThinking('Sarah is speaking...');
  }

  hideAISpeaking() {
    this.hideAIThinking();
  }

  // Menu Methods
  toggleMenu() {
    if (this.sideMenu.classList.contains('active')) {
      this.hideMenu();
    } else {
      this.showMenu();
    }
  }

  showMenu() {
    this.sideMenu.classList.add('active');
    this.menuOverlay.classList.add('active');
    this.burgerMenu.classList.add('active');
    document.body.style.overflow = 'hidden'; // Prevent background scrolling
  }

  hideMenu() {
    this.sideMenu.classList.remove('active');
    this.menuOverlay.classList.remove('active');
    this.burgerMenu.classList.remove('active');
    document.body.style.overflow = ''; // Restore scrolling
  }

  // Conversation History Methods
  async showHistory() {
    this.historyModal.style.display = 'flex';
    this.historyContent.innerHTML =
      '<div class="loading">Loading conversations...</div>';

    try {
      const response = await this.authService.makeAuthenticatedRequest(
        '/api/conversation-history'
      );
      const data = await response.json();

      if (data.success && data.conversations && data.conversations.length > 0) {
        this.renderConversationHistory(data.conversations);
      } else {
        this.historyContent.innerHTML =
          '<div class="no-history">No conversation history found.</div>';
      }
    } catch (error) {
      console.error('Error loading conversation history:', error);
      this.historyContent.innerHTML =
        '<div class="no-history">Error loading conversation history.</div>';
    }
  }

  hideHistory() {
    this.historyModal.style.display = 'none';
  }

  renderConversationHistory(conversations) {
    // Group conversations by session or time proximity
    const groupedConversations = this.groupConversations(conversations);

    // Store for access by continue conversation function
    this.allConversations = groupedConversations;

    let html = '<div class="conversation-list">';

    groupedConversations.forEach((group) => {
      const startDate = new Date(group.startTime);
      const formattedDate = startDate.toLocaleDateString();
      const formattedTime = startDate.toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
      });

      html += `
                <div class="conversation-group" data-session-id="${
                  group.sessionId
                }">
                    <div class="conversation-summary ${
                      group.messages.length <= 1 ? 'no-toggle' : ''
                    }" ${
        group.messages.length <= 1
          ? ''
          : `data-action="toggle" data-session-id="${group.sessionId}"`
      }>
                        <div class="conversation-header">
                            <h4 class="conversation-title">${group.title}</h4>
                            <div class="conversation-meta">
                                <span class="conversation-date">${formattedDate} at ${formattedTime}</span>
                                <span class="message-count">${
                                  group.messages.length
                                } messages</span>
                            </div>
                        </div>
                        <div class="conversation-preview">${this.escapeHtml(
                          group.preview
                        )}</div>
                        <div class="expand-icon" style="${
                          group.messages.length <= 1 ? 'display:none' : ''
                        }">
                            <i class="fas fa-chevron-down"></i>
                        </div>
                    </div>
                    <div class="conversation-details" id="details-${
                      group.sessionId
                    }" style="display: ${
        group.messages.length <= 1 ? 'block' : 'none'
      };">
                        ${this.renderConversationMessages(group.messages)}
                        <div class="conversation-actions">
                            <button class="continue-btn" data-action="continue" data-session-id="${
                              group.sessionId
                            }">
                                <i class="fas fa-play"></i> Continue Conversation
                            </button>
                        </div>
                    </div>
                </div>
            `;
    });

    html += '</div>';
    this.historyContent.innerHTML = html;
  }

  groupConversations(conversations) {
    // Sort conversations by timestamp
    const sorted = conversations.sort(
      (a, b) => new Date(a.timestamp) - new Date(b.timestamp)
    );

    const groups = new Map();

    sorted.forEach((conv) => {
      const sessionId =
        conv.sessionId || this.generateSessionFromTime(conv.timestamp);

      if (!groups.has(sessionId)) {
        groups.set(sessionId, {
          sessionId,
          messages: [],
          startTime: conv.timestamp,
          endTime: conv.timestamp,
        });
      }

      const group = groups.get(sessionId);
      group.messages.push(conv);
      group.endTime = conv.timestamp;
    });

    // Convert to array and add titles and previews
    return Array.from(groups.values())
      .map((group) => {
        group.title = this.generateConversationTitle(group.messages);
        group.preview = this.generateConversationPreview(group.messages);
        return group;
      })
      .sort((a, b) => new Date(b.startTime) - new Date(a.startTime));
  }

  generateSessionFromTime(timestamp) {
    // Group conversations within 30 minutes of each other
    const time = new Date(timestamp);
    const roundedTime = new Date(
      Math.floor(time.getTime() / (30 * 60 * 1000)) * (30 * 60 * 1000)
    );
    return roundedTime.getTime().toString();
  }

  generateConversationTitle(messages) {
    // Analyze messages to determine main theme
    const allText = messages
      .map((m) => m.userMessage + ' ' + m.aiResponse)
      .join(' ')
      .toLowerCase();

    // Define topic keywords and their titles
    const topics = [
      {
        keywords: ['spotify', 'music', 'streaming', 'arpu'],
        title: 'Spotify Discussion',
      },
      {
        keywords: ['apple', 'aapl', 'iphone', 'india tariff'],
        title: 'Apple Analysis',
      },
      {
        keywords: ['tesla', 'tsla', 'electric', 'elon'],
        title: 'Tesla Insights',
      },
      {
        keywords: ['bp', 'oil', 'discovery', 'brazil'],
        title: 'BP Oil Discovery',
      },
      {
        keywords: ['coinbase', 'crypto', 'bitcoin', 'convertible'],
        title: 'Coinbase & Crypto',
      },
      {
        keywords: ['palantir', 'pltr', 'data', 'analytics'],
        title: 'Palantir Updates',
      },
      {
        keywords: ['rainbow', 'rare earth', 'rbw'],
        title: 'Rainbow Rare Earths',
      },
      {
        keywords: ['market', 'stocks', 'investment', 'portfolio'],
        title: 'Market Discussion',
      },
      {
        keywords: ['chat', 'collective', 'community', 'discussed'],
        title: 'Community Chat Review',
      },
      {
        keywords: ['valuation', 'price', 'movement', 'catalyst'],
        title: 'Stock Analysis',
      },
      { keywords: ['energy', 'nuclear', 'transition'], title: 'Energy Sector' },
      {
        keywords: ['s&p', 'short', 'technical', 'drawdown'],
        title: 'Technical Analysis',
      },
    ];

    // Find the best matching topic
    for (const topic of topics) {
      const matchCount = topic.keywords.filter((keyword) =>
        allText.includes(keyword)
      ).length;
      if (matchCount >= 1) {
        return topic.title;
      }
    }

    // Default title based on first user message
    const firstMessage = messages[0]?.userMessage || '';
    if (firstMessage.length > 30) {
      return firstMessage.substring(0, 30) + '...';
    }

    return firstMessage || 'General Discussion';
  }

  generateConversationPreview(messages) {
    const firstUserMessage = messages[0]?.userMessage || '';
    return firstUserMessage.length > 80
      ? firstUserMessage.substring(0, 80) + '...'
      : firstUserMessage;
  }

  renderConversationMessages(messages) {
    let html = '<div class="message-list">';

    messages.forEach((msg) => {
      const date = new Date(msg.timestamp);
      const time = date.toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
      });

      html += `
                <div class="message-exchange">
                    <div class="message-time">${time}</div>
                    <div class="user-message">
                        <strong>You:</strong> ${this.escapeHtml(
                          msg.userMessage
                        )}
                    </div>
                    <div class="ai-message">
                        <strong>Sarah:</strong> ${this.escapeHtml(
                          msg.aiResponse
                        )}
                    </div>
                </div>
            `;
    });

    html += '</div>';
    return html;
  }

  toggleConversationDetails(sessionId) {
    const details = document.getElementById(`details-${sessionId}`);
    const icon = document.querySelector(
      `[data-session-id="${sessionId}"] .expand-icon i`
    );

    if (!details || !icon) return;

    const isHidden =
      details.style.display === 'none' || details.style.display === '';
    details.style.display = isHidden ? 'block' : 'none';
    icon.classList.toggle('fa-chevron-down', !isHidden);
    icon.classList.toggle('fa-chevron-up', isHidden);
  }

  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  async exportConversationHistory() {
    try {
      // First, try downloading the human-readable TXT export from the server
      let response = await this.authService.makeAuthenticatedRequest(
        '/api/conversation-history/download'
      );

      if (response.ok) {
        const blob = await response.blob();
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `conversation_history_${
          new Date().toISOString().split('T')[0]
        }.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        this.updateStatus('Conversation history exported successfully!');
        return;
      }

      // If TXT not available (e.g., 404), fall back to JSON export
      if (response.status !== 404) {
        // For non-404 errors, surface the error
        const errText = await response.text().catch(() => '');
        this.updateStatus(
          `Export failed (${response.status}). ${
            errText || 'Please try again.'
          }`
        );
        return;
      }

      // JSON fallback
      response = await this.authService.makeAuthenticatedRequest(
        '/api/conversation-history'
      );

      if (!response.ok) {
        const errText = await response.text().catch(() => '');
        this.updateStatus(
          `Export failed (${response.status}). ${
            errText || 'Please try again.'
          }`
        );
        return;
      }

      const data = await response.json();

      if (data.success && Array.isArray(data.conversations)) {
        const exportData = {
          exportDate: new Date().toISOString(),
          totalConversations: data.conversations.length,
          conversations: data.conversations,
        };

        const blob = new Blob([JSON.stringify(exportData, null, 2)], {
          type: 'application/json',
        });

        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `conversation-history-${
          new Date().toISOString().split('T')[0]
        }.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.updateStatus('Conversation history exported successfully!');
      } else {
        this.updateStatus('No conversation history found to export.');
      }
    } catch (error) {
      console.error('Error exporting conversation history:', error);
      this.updateStatus('Error exporting conversation history.');
    }
  }

  async clearConversationHistory() {
    if (
      confirm(
        'Are you sure you want to clear all conversation history? This action cannot be undone.'
      )
    ) {
      try {
        const response = await fetch('/api/conversation-history', {
          method: 'DELETE',
        });

        const data = await response.json();

        if (data.success) {
          this.historyContent.innerHTML =
            '<div class="no-history">No conversation history found.</div>';

          // Clear current conversation display
          this.conversationDisplay.innerHTML = `
            <div class="welcome-wrapper"
                style="position:absolute;inset:0;display:flex;align-items:center;justify-content:center;text-align:center;pointer-events:none;">
                <div class="welcome-message">
                    <i class="fas fa-robot"></i>
                    <p></p>
                </div>
            </div>
          `;

          // Show predefined prompts again
          this.showPredefinedPrompts();

          this.updateStatus('Conversation history cleared successfully!');
        } else {
          this.updateStatus('Error clearing conversation history.');
        }
      } catch (error) {
        console.error('Error clearing conversation history:', error);
        this.updateStatus('Error clearing conversation history.');
      }
    }
  }

  async continueConversation(sessionId) {
    try {
      // Find the conversation group
      const conversationGroup = this.allConversations.find(
        (group) => group.sessionId === sessionId
      );

      if (!conversationGroup) {
        this.updateStatus('Conversation not found.');
        return;
      }

      // Load the conversation context
      this.loadConversationContext(conversationGroup);

      // Close the history modal
      this.hideHistory();
      this.hideMenu();

      // Show a message indicating the conversation has been resumed
      this.addMessageToDisplay(
        'system',
        `📝 Resumed conversation: "${conversationGroup.title}"`
      );

      // Scroll to bottom to show the resume message
      this.scrollToBottom();

      this.updateStatus(`Conversation resumed: ${conversationGroup.title}`);
    } catch (error) {
      console.error('Error continuing conversation:', error);
      this.updateStatus('Error resuming conversation.');
    }
  }

  loadConversationContext(conversationGroup) {
    // Set the current session ID
    this.currentSessionId = conversationGroup.sessionId;

    // Load conversation history for context
    this.conversationHistory = [];
    conversationGroup.messages.forEach((msg) => {
      this.conversationHistory.push(
        { role: 'user', content: msg.userMessage },
        { role: 'assistant', content: msg.aiResponse }
      );
    });

    // Keep conversation history manageable (last 20 exchanges)
    if (this.conversationHistory.length > 20) {
      this.conversationHistory = this.conversationHistory.slice(-20);
    }

    // Load any lead data from the last message
    const lastMessage =
      conversationGroup.messages[conversationGroup.messages.length - 1];
    if (
      lastMessage &&
      lastMessage.leadData &&
      Object.keys(lastMessage.leadData).length > 0
    ) {
      this.leadData = { ...lastMessage.leadData };
      this.updateLeadDisplay();
    }

    // Update session ID for new messages
    this.sessionId = conversationGroup.sessionId;
  }

  addMessageToDisplay(sender, message) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}-message`;

    if (sender === 'system') {
      messageDiv.innerHTML = `<div class="system-message">${message}</div>`;
      messageDiv.style.textAlign = 'center';
      messageDiv.style.color = '#888';
      messageDiv.style.fontStyle = 'italic';
      messageDiv.style.margin = '10px 0';
    } else {
      const senderLabel = sender === 'user' ? 'You' : 'Sarah';
      messageDiv.innerHTML = `<strong>${senderLabel}:</strong> ${message}`;
    }

    this.conversationDisplay.appendChild(messageDiv);
    this.scrollToBottom();
  }

  scrollToBottom() {
    const el = this.conversationDisplay;
    if (!el) return;
    try {
      // Primary set
      el.scrollTop = el.scrollHeight;
      // Explicit API if available
      if (typeof el.scrollTo === 'function') {
        el.scrollTo({ top: el.scrollHeight, behavior: 'auto' });
      }
      // Ensure last element is visible
      const last = el.lastElementChild;
      if (last && typeof last.scrollIntoView === 'function') {
        last.scrollIntoView({ block: 'end' });
      }
      // Re-apply after layout/paint
      requestAnimationFrame(() => {
        el.scrollTop = el.scrollHeight;
      });
      setTimeout(() => {
        el.scrollTop = el.scrollHeight;
      }, 50);
    } catch (_) {
      // no-op
    }
  }

  updateLayoutInsets() {
    try {
      // Reserve bottom space equal to voice controls height + its bottom offset + small gap
      if (this.voiceControls && this.conversationDisplay) {
        const vcStyles = window.getComputedStyle(this.voiceControls);
        const bottom = parseInt(vcStyles.bottom) || 0;
        let reserve = this.voiceControls.offsetHeight + bottom + 16;

        // Add extra space if predefined prompts are visible
        if (this.predefinedPrompts && !this.predefinedPrompts.classList.contains('hidden')) {
          reserve += this.predefinedPrompts.offsetHeight + 20; // Add prompts height + gap
        }

        this.conversationDisplay.style.paddingBottom = `${reserve}px`;
      }

      // Ensure main content top padding matches header height
      if (this.headerEl && this.mainContentEl) {
        const reserveTop = this.headerEl.offsetHeight + 20;
        this.mainContentEl.style.paddingTop = `${reserveTop}px`;
      }
    } catch (e) {
      console.warn('updateLayoutInsets error:', e);
    }
  }

  /**
   * Stock Preview Card Methods
   */

  /**
   * Show stock preview card with full stock data
   * @param {Object} stockData - Complete stock object with all data
   */
  showStockPreview(stockData) {
    if (
      !this.stockPreviewCard ||
      !this.previewCompanyName ||
      !this.previewTicker
    ) {
      console.warn('Stock preview elements not found');
      return;
    }

    // Update basic info
    this.previewCompanyName.textContent = stockData.companyName;
    this.previewTicker.textContent = stockData.ticker;

    // Update price info
    if (this.previewPrice) {
      this.previewPrice.textContent = `$${stockData.price}`;
    }

    // Update price change with color coding
    if (this.previewChange) {
      const changePercent = stockData.changePercent;
      const changeText =
        changePercent > 0 ? `+${changePercent}%` : `${changePercent}%`;
      this.previewChange.textContent = changeText;

      // Add appropriate class for color
      this.previewChange.classList.remove('positive', 'negative');
      this.previewChange.classList.add(
        changePercent >= 0 ? 'positive' : 'negative'
      );
    }

    // Update market cap
    if (this.previewMarketCap) {
      this.previewMarketCap.textContent = stockData.marketCap;
    }

    // Update price target
    if (this.previewTarget) {
      this.previewTarget.textContent = `PT: $${stockData.priceTarget}`;
    }

    // Show the card with animation
    this.stockPreviewCard.style.display = 'block';

    // Force reflow to ensure display change is applied
    this.stockPreviewCard.offsetHeight;

    // Add show class for smooth animation
    this.stockPreviewCard.classList.add('show', 'animate-in');
    this.stockPreviewCard.classList.remove('animate-out');

    console.log(
      `📊 Showing stock preview: ${stockData.companyName} (${
        stockData.ticker
      }) - $${stockData.price} (${stockData.changePercent > 0 ? '+' : ''}${
        stockData.changePercent
      }%)`
    );
  }

  /**
   * Hide stock preview card with animation
   */
  hideStockPreview() {
    if (!this.stockPreviewCard) {
      return;
    }

    // Add exit animation
    this.stockPreviewCard.classList.add('animate-out');
    this.stockPreviewCard.classList.remove('show', 'animate-in');

    // Hide after animation completes
    setTimeout(() => {
      this.stockPreviewCard.style.display = 'none';
      this.stockPreviewCard.classList.remove('animate-out');
    }, 300);

    console.log('📊 Hiding stock preview');
  }

  /**
   * Show stock preview for detected stocks during AI speech
   * @param {Array} detectedStocks - Array of detected stock objects
   */
  showStockPreviewForSpeech(detectedStocks) {
    if (!detectedStocks || detectedStocks.length === 0) {
      return;
    }

    // Show the first detected stock (highest confidence)
    const primaryStock = detectedStocks.sort(
      (a, b) =>
        (b.matchDetails?.confidence || 0) - (a.matchDetails?.confidence || 0)
    )[0];

    this.showStockPreview(primaryStock);

    // No auto-hide timeout - will be hidden when speech ends
    console.log('📊 Stock preview will remain visible during entire AI speech');
  }

  /**
   * Handle stock preview during AI speech
   * @param {string} aiResponseText - The AI response text to analyze
   */
  async handleStockPreviewForSpeech(aiResponseText) {
    try {
      // Detect stocks in the AI response
      const detectedStocks = await this.detectStocksInResponse(aiResponseText);

      if (detectedStocks.length > 0) {
        console.log(
          `📊 Detected ${detectedStocks.length} stock(s) in AI response:`,
          detectedStocks.map((s) => s.ticker).join(', ')
        );

        // Show preview for the detected stocks
        this.showStockPreviewForSpeech(detectedStocks);
      }
    } catch (error) {
      console.error('Error handling stock preview:', error);
    }
  }

  /**
   * Detect stocks in AI response text
   * @param {string} text - AI response text to analyze
   * @returns {Array} Array of detected stocks with full data
   */
  async detectStocksInResponse(text) {
    try {
      if (!text || typeof text !== 'string') {
        return [];
      }

      // Use the backend stock detection service
      const response = await this.authService.makeAuthenticatedRequest(
        '/api/detect-stocks',
        {
          method: 'POST',
          body: JSON.stringify({ text }),
        }
      );

      if (!response.ok) {
        console.error('Stock detection API error:', response.status);
        return [];
      }

      const result = await response.json();

      if (result.success && result.stocks) {
        console.log(
          `📊 Backend detected ${result.count} stock(s) in text: "${result.text}"`
        );
        console.log(
          '📊 Detected stocks:',
          result.stocks
            .map(
              (s) =>
                `${s.ticker} (confidence: ${
                  s.matchDetails?.confidence || 'N/A'
                })`
            )
            .join(', ')
        );

        // Log match details for debugging
        result.stocks.forEach((stock) => {
          if (stock.matchDetails) {
            console.log(`📊 ${stock.ticker} matches:`, stock.matchDetails);
          }
        });

        // Filter backend results to avoid false positives not present in text
        const textLower = (text || '').toLowerCase();
        const backendStocks = Array.isArray(result.stocks) ? result.stocks : [];
        const filteredBackend = backendStocks.filter((s) => {
          if (!s) return false;
          const tMatch = s.ticker
            ? new RegExp(`\\b${s.ticker}\\b`, 'i').test(text)
            : false;
          const cname = s.companyName ? String(s.companyName) : '';
          const nMatch = cname
            ? textLower.includes(cname.toLowerCase())
            : false;
          // Only accept when ticker or company name appears in text to avoid mismatches
          return tMatch || nMatch;
        });

        // Also run local pattern detector for additional matches (e.g., WBD)
        const localStocks = await this.detectStocksInResponseLegacy(text);

        // Merge and de-duplicate by ticker
        const combined = [...filteredBackend, ...localStocks];
        const unique = combined.filter(
          (stk, idx, arr) =>
            stk &&
            stk.ticker &&
            idx === arr.findIndex((s2) => s2.ticker === stk.ticker)
        );

        return unique;
      }

      return [];
    } catch (error) {
      console.error('Error detecting stocks:', error);
      // Fallback to empty array instead of crashing
      return [];
    }
  }

  // Legacy fallback method (keeping for reference)
  async detectStocksInResponseLegacy(text) {
    try {
      // Stock data from our stockData.json
      const stockDatabase = {
        'AAPL': {
          ticker: 'AAPL',
          companyName: 'Apple Inc.',
          price: 189.84,
          changePercent: 2.34,
          marketCap: '2.89T',
          priceTarget: 220.0,
          sector: 'Technology',
        },
        'TSLA': {
          ticker: 'TSLA',
          companyName: 'Tesla, Inc.',
          price: 248.5,
          changePercent: -1.87,
          marketCap: '789.2B',
          priceTarget: 300.0,
          sector: 'Consumer Discretionary',
        },
        'NVDA': {
          ticker: 'NVDA',
          companyName: 'NVIDIA Corporation',
          price: 118.75,
          changePercent: 3.45,
          marketCap: '2.92T',
          priceTarget: 140.0,
          sector: 'Technology',
        },
        'SPOT': {
          ticker: 'SPOT',
          companyName: 'Spotify Technology S.A.',
          price: 342.18,
          changePercent: 1.23,
          marketCap: '68.5B',
          priceTarget: 380.0,
          sector: 'Communication Services',
        },
        'META': {
          ticker: 'META',
          companyName: 'Meta Platforms, Inc.',
          price: 512.33,
          changePercent: 0.89,
          marketCap: '1.31T',
          priceTarget: 580.0,
          sector: 'Communication Services',
        },
        'PLTR': {
          ticker: 'PLTR',
          companyName: 'Palantir Technologies Inc.',
          price: 28.45,
          changePercent: 4.67,
          marketCap: '62.8B',
          priceTarget: 35.0,
          sector: 'Technology',
        },
        'COIN': {
          ticker: 'COIN',
          companyName: 'Coinbase Global, Inc.',
          price: 195.67,
          changePercent: -2.14,
          marketCap: '48.2B',
          priceTarget: 250.0,
          sector: 'Financial Services',
        },
        'QCOM': {
          ticker: 'QCOM',
          companyName: 'QUALCOMM Incorporated',
          price: 168.92,
          changePercent: 1.56,
          marketCap: '188.4B',
          priceTarget: 190.0,
          sector: 'Technology',
        },
        'WBD': {
          ticker: 'WBD',
          companyName: 'Warner Bros. Discovery, Inc.',
          price: 8.12,
          changePercent: -0.52,
          marketCap: '19.7B',
          priceTarget: 12.0,
          sector: 'Communication Services',
        },
      };

      const stockPatterns = [
        { pattern: /\b(AAPL|Apple Inc\.?|Apple)\b/gi, ticker: 'AAPL' },
        { pattern: /\b(TSLA|Tesla Inc\.?|Tesla)\b/gi, ticker: 'TSLA' },
        { pattern: /\b(NVDA|NVIDIA|Nvidia)\b/gi, ticker: 'NVDA' },
        { pattern: /\b(SPOT|Spotify)\b/gi, ticker: 'SPOT' },
        { pattern: /\b(META|Meta|Facebook)\b/gi, ticker: 'META' },
        { pattern: /\b(PLTR|Palantir)\b/gi, ticker: 'PLTR' },
        { pattern: /\b(COIN|Coinbase)\b/gi, ticker: 'COIN' },
        { pattern: /\b(QCOM|Qualcomm)\b/gi, ticker: 'QCOM' },
        {
          pattern:
            /\b(WBD|Warner\s?Bros\.?|Warner\s?Brothers|Warner\s?Bros\.?\s?Discovery|Warner\s?Brothers\s?Discovery|Warner\s?Discovery)\b/gi,
          ticker: 'WBD',
        },
      ];

      const detectedStocks = [];

      stockPatterns.forEach(({ pattern, ticker }) => {
        if (pattern.test(text)) {
          const stockData = stockDatabase[ticker];
          if (stockData) {
            detectedStocks.push({
              ...stockData,
              matchDetails: { confidence: 0.8 },
            });
          }
        }
      });

      // Remove duplicates
      const uniqueStocks = detectedStocks.filter(
        (stock, index, self) =>
          index === self.findIndex((s) => s.ticker === stock.ticker)
      );

      return uniqueStocks;
    } catch (error) {
      console.error('Error detecting stocks:', error);
      return [];
    }
  }
}

// Initialize the app when the page loads
let app;
document.addEventListener('DOMContentLoaded', () => {
  app = new VoiceLeadApp();
});

// Global function for debugging microphone button visibility
window.debugMicButton = function () {
  if (app && app.checkButtonVisibility) {
    app.checkButtonVisibility();
  } else {
    console.log(
      'App not initialized or checkButtonVisibility method not available'
    );
  }
};
