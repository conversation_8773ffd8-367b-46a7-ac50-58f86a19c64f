# Echo Mobile API Specification

## Overview

This document specifies the enhanced API endpoints required to support the Flutter mobile applications for the Echo (ShareFlix) voice-driven investment platform. These APIs extend the existing Node.js backend to provide mobile-optimized functionality while maintaining backward compatibility with the web application.

## Base Configuration

**Base URL**: `https://api.shareflix.com` (production) / `http://localhost:3000` (development)  
**Authentication**: Bearer JWT tokens + Access password  
**Content-Type**: `application/json` (except file uploads)  
**API Version**: v1 (current), v2 (mobile-enhanced)  

## Authentication Flow

### Access Control (Application Level)
```http
POST /api/access/verify
Content-Type: application/json

{
  "password": "echo2025"
}

Response 200:
{
  "success": true,
  "message": "Access granted",
  "expiresIn": 86400000
}
```

### Session Management (Mobile)
```http
POST /api/session/create
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "deviceId": "device_abc123",
  "platform": "ios|android",
  "appVersion": "1.0.0"
}

Response 201:
{
  "success": true,
  "sessionId": "session_def456",
  "sessionToken": "jwt_session_token",
  "expiresAt": "2025-01-02T10:00:00Z",
  "userPreferences": {
    "voiceEnabled": true,
    "notificationsEnabled": false
  }
}
```

## Enhanced Conversation Management

### Grouped Conversation History
```http
GET /api/conversation-history/grouped?limit=50&offset=0
Authorization: Bearer {session_token}

Response 200:
{
  "success": true,
  "conversationGroups": [
    {
      "sessionId": "session_123",
      "title": "Investment Goals Discussion",
      "preview": "I'm interested in long-term growth strategies...",
      "startTime": "2025-01-01T10:00:00Z",
      "endTime": "2025-01-01T10:30:00Z",
      "messageCount": 8,
      "leadScore": 75,
      "tags": ["stocks", "retirement", "growth"],
      "messages": [
        {
          "id": "msg_001",
          "timestamp": "2025-01-01T10:00:00Z",
          "userMessage": "I want to start investing",
          "aiResponse": "That's great! Let's discuss your goals...",
          "detectedStocks": [],
          "leadData": {
            "experience": "beginner",
            "confidence": 0.8
          }
        }
      ]
    }
  ],
  "pagination": {
    "total": 25,
    "limit": 50,
    "offset": 0,
    "hasMore": false
  }
}
```

### Resume Conversation
```http
POST /api/conversation/continue/:sessionId
Content-Type: application/json
Authorization: Bearer {session_token}

{
  "sessionId": "session_123"
}

Response 200:
{
  "success": true,
  "conversationContext": [
    {
      "role": "user",
      "content": "I want to invest in tech stocks"
    },
    {
      "role": "assistant", 
      "content": "Technology stocks can be great for growth..."
    }
  ],
  "leadData": {
    "name": "John Doe",
    "portfolioSize": 100000,
    "experience": "intermediate",
    "qualificationScore": 75
  },
  "sessionState": {
    "currentTopic": "tech_stocks",
    "nextSuggestedQuestions": [
      "What's your risk tolerance?",
      "Are you interested in dividend stocks?"
    ]
  }
}
```

### Conversation Search
```http
GET /api/conversation/search?query=apple&limit=20&includeContext=true
Authorization: Bearer {session_token}

Response 200:
{
  "success": true,
  "results": [
    {
      "sessionId": "session_456",
      "messageId": "msg_789",
      "relevanceScore": 0.95,
      "matchedText": "I think Apple (AAPL) is a solid investment",
      "context": {
        "userMessage": "What do you think about Apple?",
        "aiResponse": "Apple (AAPL) is a solid long-term investment...",
        "timestamp": "2025-01-01T15:30:00Z"
      },
      "detectedStocks": [
        {
          "ticker": "AAPL",
          "confidence": 0.95
        }
      ]
    }
  ],
  "searchMetadata": {
    "query": "apple",
    "totalResults": 5,
    "searchTime": "0.045s"
  }
}
```

## Enhanced Stock Detection & Preview

### Stock Detection in Conversation
```http
POST /api/detect-stocks-in-conversation
Content-Type: application/json
Authorization: Bearer {session_token}

{
  "userMessage": "What do you think about Apple and Tesla?",
  "aiResponse": "Apple (AAPL) and Tesla (TSLA) are both strong companies...",
  "includePreviewData": true
}

Response 200:
{
  "success": true,
  "detectedStocks": [
    {
      "ticker": "AAPL",
      "companyName": "Apple Inc.",
      "price": 189.84,
      "changePercent": 2.34,
      "marketCap": "2.89T",
      "matchDetails": {
        "confidence": 0.95,
        "source": "ai_response",
        "matchedTerms": ["Apple", "AAPL"],
        "position": 15
      },
      "previewData": {
        "priceTarget": 220.00,
        "analystRating": "Buy",
        "peRatio": 28.5,
        "dividendYield": 0.52,
        "thesis": "Leading consumer technology company with strong ecosystem..."
      }
    },
    {
      "ticker": "TSLA",
      "companyName": "Tesla, Inc.",
      "price": 248.50,
      "changePercent": -1.87,
      "marketCap": "789.2B",
      "matchDetails": {
        "confidence": 0.90,
        "source": "ai_response",
        "matchedTerms": ["Tesla", "TSLA"],
        "position": 35
      },
      "previewData": {
        "priceTarget": 300.00,
        "analystRating": "Hold",
        "peRatio": 65.2,
        "dividendYield": 0.00,
        "thesis": "EV market leader with expanding energy business..."
      }
    }
  ],
  "processingTime": "0.023s"
}
```

### Stock Preview Data
```http
GET /api/stock-preview/:ticker?includeNews=true
Authorization: Bearer {session_token}

Response 200:
{
  "success": true,
  "stock": {
    "ticker": "AAPL",
    "companyName": "Apple Inc.",
    "price": 189.84,
    "changePercent": 2.34,
    "changeAmount": 4.35,
    "marketCap": "2.89T",
    "volume": "45.2M",
    "priceTarget": 220.00,
    "analystRating": "Buy",
    "peRatio": 28.5,
    "dividendYield": 0.52,
    "sector": "Technology",
    "industry": "Consumer Electronics",
    "thesis": "Leading consumer technology company with strong ecosystem, services growth, and AI integration potential.",
    "keyMetrics": {
      "revenue": "394.3B",
      "netIncome": "99.8B",
      "eps": 6.16,
      "bookValue": 4.84
    },
    "recentNews": [
      {
        "headline": "Apple Reports Strong Q4 Earnings",
        "summary": "Revenue beats expectations...",
        "publishedAt": "2025-01-01T09:00:00Z",
        "source": "Reuters"
      }
    ],
    "lastUpdated": "2025-01-01T16:00:00Z"
  }
}
```

## Lead Qualification API

### Extract Lead Information
```http
POST /api/lead/extract
Content-Type: application/json
Authorization: Bearer {session_token}

{
  "userMessage": "I have about $100,000 to invest for retirement",
  "aiResponse": "That's a substantial portfolio for retirement planning...",
  "conversationHistory": [
    {
      "role": "user",
      "content": "I'm 45 years old and want to retire at 65"
    }
  ],
  "sessionId": "session_123"
}

Response 200:
{
  "success": true,
  "extractedData": {
    "portfolioSize": {
      "value": 100000,
      "confidence": 0.9,
      "source": "user_message"
    },
    "investmentGoal": {
      "value": "retirement",
      "confidence": 0.95,
      "source": "user_message"
    },
    "timeline": {
      "value": "long_term",
      "confidence": 0.8,
      "source": "conversation_context"
    },
    "age": {
      "value": 45,
      "confidence": 0.9,
      "source": "conversation_history"
    }
  },
  "qualificationScore": 85,
  "scoreBreakdown": {
    "portfolioSize": 30,
    "experience": 20,
    "engagement": 25,
    "timeline": 10
  },
  "recommendation": "high_priority",
  "nextQuestions": [
    "What's your current investment experience?",
    "How would you describe your risk tolerance?",
    "Do you currently work with a financial advisor?"
  ],
  "processingTime": "0.156s"
}
```

### Lead Qualification Score
```http
GET /api/lead/qualification-score/:sessionId
Authorization: Bearer {session_token}

Response 200:
{
  "success": true,
  "sessionId": "session_123",
  "qualificationScore": 85,
  "scoreHistory": [
    {
      "score": 45,
      "timestamp": "2025-01-01T10:00:00Z",
      "trigger": "initial_contact"
    },
    {
      "score": 65,
      "timestamp": "2025-01-01T10:15:00Z", 
      "trigger": "portfolio_size_revealed"
    },
    {
      "score": 85,
      "timestamp": "2025-01-01T10:30:00Z",
      "trigger": "investment_goals_clarified"
    }
  ],
  "factors": {
    "portfolioSize": {
      "score": 30,
      "weight": 0.35,
      "value": 100000,
      "category": "substantial"
    },
    "experience": {
      "score": 20,
      "weight": 0.25,
      "value": "intermediate",
      "category": "some_experience"
    },
    "engagement": {
      "score": 25,
      "weight": 0.25,
      "value": "high",
      "category": "actively_asking_questions"
    },
    "timeline": {
      "score": 10,
      "weight": 0.15,
      "value": "long_term",
      "category": "retirement_planning"
    }
  },
  "recommendation": {
    "priority": "high_priority",
    "action": "schedule_consultation",
    "reasoning": "Substantial portfolio with clear investment goals and high engagement"
  }
}
```

## Mobile-Optimized Audio Processing

### Combined Voice Message Processing
```http
POST /api/audio/process-voice-message
Content-Type: multipart/form-data
Authorization: Bearer {session_token}

Form Data:
- audio: [audio file]
- sessionId: "session_123"
- includeStockDetection: true
- includeTTS: true

Response 200:
{
  "success": true,
  "transcription": {
    "text": "What stocks should I buy for long-term growth?",
    "confidence": 0.95,
    "language": "en",
    "processingTime": "1.2s"
  },
  "aiResponse": {
    "text": "For long-term growth, I'd recommend looking at technology stocks like Apple (AAPL) and Microsoft (MSFT)...",
    "provider": "openai",
    "processingTime": "2.1s"
  },
  "audioResponse": {
    "url": "/api/audio/response/abc123.mp3",
    "duration": 15.5,
    "format": "mp3",
    "size": 248576
  },
  "detectedStocks": [
    {
      "ticker": "AAPL",
      "confidence": 0.95,
      "previewData": { /* stock data */ }
    },
    {
      "ticker": "MSFT", 
      "confidence": 0.90,
      "previewData": { /* stock data */ }
    }
  ],
  "leadData": {
    "extractedInfo": {
      "investmentGoal": "long_term_growth"
    },
    "qualificationScore": 70
  },
  "sessionUpdated": true,
  "totalProcessingTime": "3.8s"
}
```

### Cached Welcome Message
```http
GET /api/audio/welcome-message?voice=alloy&format=mp3
Authorization: Bearer {session_token}

Response 200:
Content-Type: audio/mpeg
Content-Length: 156789
Cache-Control: public, max-age=3600
ETag: "welcome-v1-alloy-mp3"

[Audio binary data]
```

## Session State Management

### Update Session State
```http
PUT /api/session/state/:sessionId
Content-Type: application/json
Authorization: Bearer {session_token}

{
  "conversationContext": [
    {
      "role": "user",
      "content": "I want to invest in tech stocks"
    }
  ],
  "leadData": {
    "portfolioSize": 100000,
    "experience": "intermediate"
  },
  "uiState": {
    "lastScreen": "chat",
    "voiceEnabled": true,
    "currentTopic": "tech_stocks"
  },
  "preferences": {
    "notificationsEnabled": true,
    "autoPlayTTS": true
  }
}

Response 200:
{
  "success": true,
  "sessionId": "session_123",
  "stateUpdated": true,
  "timestamp": "2025-01-01T16:30:00Z"
}
```

### Retrieve Session State
```http
GET /api/session/state/:sessionId
Authorization: Bearer {session_token}

Response 200:
{
  "success": true,
  "sessionId": "session_123",
  "sessionState": {
    "conversationContext": [ /* conversation history */ ],
    "leadData": { /* lead qualification data */ },
    "uiState": {
      "lastScreen": "chat",
      "voiceEnabled": true,
      "currentTopic": "tech_stocks",
      "scrollPosition": 0
    },
    "preferences": {
      "notificationsEnabled": true,
      "autoPlayTTS": true,
      "voiceSpeed": 1.0
    }
  },
  "lastUpdated": "2025-01-01T16:30:00Z",
  "expiresAt": "2025-01-02T16:30:00Z"
}
```

## Error Handling

### Standard Error Response Format
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input parameters",
    "details": {
      "field": "portfolioSize",
      "reason": "Must be a positive number"
    }
  },
  "timestamp": "2025-01-01T16:30:00Z",
  "requestId": "req_abc123"
}
```

### Common Error Codes
- `ACCESS_REQUIRED` - Access password needed
- `INVALID_SESSION` - Session expired or invalid
- `VALIDATION_ERROR` - Input validation failed
- `RATE_LIMIT_EXCEEDED` - Too many requests
- `AI_SERVICE_UNAVAILABLE` - AI providers down
- `AUDIO_PROCESSING_FAILED` - Voice processing error
- `STOCK_DATA_UNAVAILABLE` - Stock service error

## Rate Limiting

- **General API**: 100 requests/minute per session
- **Voice Processing**: 20 requests/minute per session  
- **Stock Detection**: 50 requests/minute per session
- **Conversation History**: 30 requests/minute per session

Rate limit headers included in all responses:
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: **********
```

---

*This API specification supports the Flutter mobile migration while maintaining backward compatibility with the existing web application.*
