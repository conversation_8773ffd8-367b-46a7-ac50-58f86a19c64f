# Echo Application Specification

## Table of Contents
1. [Application Overview](#application-overview)
2. [Core Features](#core-features)
3. [Technical Architecture](#technical-architecture)
4. [User Interface Components](#user-interface-components)
5. [Backend Services](#backend-services)
6. [API Endpoints](#api-endpoints)
7. [Data Models](#data-models)
8. [Security Features](#security-features)
9. [AI Integration](#ai-integration)
10. [Voice Processing](#voice-processing)
11. [Stock Detection System](#stock-detection-system)
12. [Conversation Management](#conversation-management)
13. [User Workflows](#user-workflows)
14. [Deployment Architecture](#deployment-architecture)

## Application Overview

### Purpose
Echo (branded as ShareFlix) is a voice-driven investment discovery platform designed for elite investors. It serves as an AI-powered lead qualification system that combines voice interaction, intelligent conversation management, and real-time stock analysis to facilitate investment discussions and lead generation.

### Core Value Proposition
- **Voice-First Experience**: Natural speech-to-text and text-to-speech interactions
- **Investment Intelligence**: AI assistant trained on investment terminology and market insights
- **Stock Context**: Automatic detection and preview of stocks mentioned in conversations
- **Lead Qualification**: Systematic extraction and storage of lead information
- **Conversation Continuity**: Persistent conversation history with smart context loading

### Target Users
- Investment advisors and financial professionals
- Elite investors seeking market insights
- Financial service providers for lead qualification
- Investment community members

## Core Features

### 1. Voice Interaction System
- **Real-time Speech Recognition**: OpenAI Whisper API integration
- **Natural Text-to-Speech**: OpenAI TTS with "alloy" voice
- **Toggle Voice Mode**: Press-to-activate/deactivate voice recording
- **Audio Visualization**: Blob animation during AI speech
- **Welcome Audio**: Automatic greeting on page load

### 2. AI-Powered Conversations
- **Dual AI Provider Support**: OpenAI GPT-4 (primary) with Claude fallback
- **Investment-Focused Prompts**: Specialized for stock market discussions
- **Lead Qualification**: Systematic gathering of investment goals and contact info
- **Market Insights**: Integration of recent market conversations and analysis
- **Context Awareness**: Smart loading of relevant conversation history

### 3. Stock Detection & Preview
- **Multi-Method Detection**: Ticker symbols, company names, and keywords
- **Real-time Preview Cards**: Visual stock information during AI speech
- **Confidence Scoring**: Weighted detection with match confidence levels
- **Stock Data Integration**: Price, market cap, analyst ratings, and investment thesis

### 4. Conversation Management
- **Encrypted Storage**: AES-256 encryption for sensitive conversation data
- **Dual Format Storage**: JSON (system use) and TXT (human readable)
- **History Browsing**: Searchable conversation history with resume capability
- **Smart Context Loading**: Relevance-based conversation retrieval
- **Collective Chat Integration**: Community investment discussions

### 5. Security & Access Control
- **Password Protection**: Application-level access control
- **Session Management**: Secure cookie-based sessions
- **Input Validation**: XSS protection and sanitization
- **Rate Limiting**: API abuse prevention
- **Secure File Handling**: Restricted audio file uploads

## Technical Architecture

### Frontend Stack
- **HTML5**: Semantic markup with accessibility features
- **CSS3**: Responsive design with animations and transitions
- **Vanilla JavaScript**: No framework dependencies for performance
- **Web APIs**: MediaDevices, Audio, Fetch for browser integration

### Backend Stack
- **Node.js**: Runtime environment
- **Express.js**: Web application framework
- **Middleware Stack**:
  - Helmet: Security headers
  - CORS: Cross-origin resource sharing
  - Rate limiting: API protection
  - Cookie parser: Session management
  - Multer: File upload handling

### External Integrations
- **OpenAI APIs**:
  - GPT-4: Chat completions
  - Whisper: Speech-to-text
  - TTS: Text-to-speech
- **Anthropic Claude**: Fallback AI provider
- **File System**: Local data storage with encryption

### Data Storage
- **Conversation History**: Encrypted JSON + readable TXT
- **User Data**: Encrypted JSON with bcrypt password hashing
- **Stock Data**: Static JSON with market information
- **Collective Chats**: Community conversation archives

## User Interface Components

### Main Interface Elements
1. **Header**: ShareFlix branding and subtitle
2. **Conversation Display**: Scrollable message history
3. **Predefined Prompts**: Quick-start conversation buttons
4. **Voice Controls**: Microphone button with visual feedback
5. **Text Input**: Alternative to voice with send button
6. **Stock Preview Card**: Contextual stock information overlay
7. **Status Indicator**: Real-time system status
8. **History Modal**: Conversation browsing interface

### Visual Design
- **Color Scheme**: White background (#FFFFFF) with yellow accent (#FFEE8C)
- **Typography**: 14px base font size for readability
- **Animations**: Smooth transitions and blob animations
- **Responsive Design**: Mobile-first approach with breakpoints
- **Accessibility**: ARIA labels and keyboard navigation

### Interactive Elements
- **Microphone Button**: Toggle voice recording with visual states
- **Prompt Buttons**: Pre-defined conversation starters
- **Stock Cards**: Expandable information with flip animations
- **History Navigation**: Expandable conversation groups
- **Continue Conversation**: Resume previous discussions

## Backend Services

### Core Services

#### 1. AI Service (`services/aiService.js`)
- **Provider Management**: OpenAI and Claude integration
- **Automatic Fallback**: Seamless provider switching
- **Model Mapping**: OpenAI to Claude model conversion
- **Error Handling**: Provider availability detection
- **Mock Mode**: Testing without API calls

#### 2. Stock Detection Service (`services/stockDetectionService.js`)
- **Multi-Method Detection**: Tickers, company names, keywords
- **Confidence Scoring**: Weighted relevance calculation
- **Stock Database**: 10+ major stocks with metadata
- **Performance Optimization**: Efficient regex and mapping

#### 3. Encryption Service (`services/encryption.js`)
- **AES-256 Encryption**: Secure data storage
- **Key Management**: Automatic key generation
- **Field-Level Encryption**: Selective data protection
- **Secure Hashing**: Password and token generation

### Middleware Components

#### 1. Access Control (`middleware/accessControl.js`)
- **Password Protection**: Application-level security
- **Session Management**: Token-based authentication
- **Route Protection**: Selective endpoint security
- **Development Bypass**: Safari compatibility mode

#### 2. Authentication (`middleware/auth.js`)
- **JWT Tokens**: Secure user sessions
- **User Management**: Registration and login
- **Role-Based Access**: Admin and user permissions
- **Password Hashing**: bcrypt implementation

#### 3. File Upload (`middleware/fileUpload.js`)
- **Secure Upload**: Audio file restrictions
- **File Validation**: MIME type and size limits
- **Temporary Storage**: Automatic cleanup
- **Security Checks**: Malicious file prevention

#### 4. Validation (`middleware/validation.js`)
- **Input Sanitization**: XSS prevention
- **Data Validation**: Type and format checking
- **Error Handling**: Structured validation responses
- **Rate Limiting**: Request throttling

## API Endpoints

### Core Endpoints

#### Chat & AI
- `POST /api/chat` - AI conversation processing
- `GET /api/ai-status` - Service status and configuration
- `GET /api/ai-health` - Provider health checks

#### Voice Processing
- `POST /api/speech-to-text` - Audio transcription
- `POST /api/text-to-speech` - Speech synthesis
- `POST /api/voice-introduction` - Welcome message generation

#### Stock Analysis
- `POST /api/detect-stocks` - Stock mention detection
- `GET /api/stock-info/:ticker` - Individual stock data
- `POST /api/search-stocks` - Stock search functionality

#### Conversation Management
- `GET /api/conversation-history` - Retrieve conversation history
- `DELETE /api/conversation-history` - Clear conversation data
- `POST /api/save-lead` - Store lead information

#### Access Control
- `POST /api/access/verify` - Password verification
- `GET /api/access/status` - Access session status

#### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User authentication
- `GET /api/auth/profile` - User profile data

### Response Formats

#### Standard Success Response
```json
{
  "success": true,
  "data": {},
  "timestamp": "2025-01-01T00:00:00.000Z"
}
```

#### Error Response
```json
{
  "error": "Error message",
  "code": "ERROR_CODE",
  "timestamp": "2025-01-01T00:00:00.000Z"
}
```

#### AI Chat Response
```json
{
  "response": "AI response text",
  "provider": "openai",
  "fallback": false,
  "detectedStocks": [],
  "leadData": {}
}
```

## Data Models

### Conversation Exchange
```json
{
  "id": "unique-identifier",
  "timestamp": "ISO-8601-datetime",
  "userMessage": "encrypted-user-input",
  "aiResponse": "encrypted-ai-response",
  "leadData": "encrypted-lead-info",
  "userId": "user-identifier",
  "sessionId": "session-identifier"
}
```

### Stock Data
```json
{
  "ticker": "AAPL",
  "companyName": "Apple Inc.",
  "price": 189.84,
  "changePercent": 2.34,
  "marketCap": "2.89T",
  "priceTarget": 220.00,
  "sector": "Technology",
  "industry": "Consumer Electronics",
  "thesis": "Investment thesis text",
  "keywords": ["apple", "iphone", "services"],
  "analystRating": "Buy",
  "peRatio": 28.5,
  "dividendYield": 0.52
}
```

### Lead Information
```json
{
  "name": "Contact name",
  "email": "<EMAIL>",
  "phone": "phone-number",
  "investmentExperience": "beginner|intermediate|advanced",
  "portfolioSize": 50000,
  "investmentGoals": "retirement|wealth-building|income",
  "riskTolerance": "conservative|moderate|aggressive",
  "timeline": "short-term|medium-term|long-term",
  "hasAdvisor": true,
  "qualificationScore": 85
}
```

### User Account
```json
{
  "id": "unique-user-id",
  "username": "username",
  "email": "encrypted-email",
  "password": "bcrypt-hash",
  "role": "user|admin",
  "isActive": true,
  "createdAt": "ISO-8601-datetime",
  "lastLogin": "ISO-8601-datetime"
}
```

## Security Features

### Data Protection
- **Encryption at Rest**: AES-256 for sensitive data
- **Secure Transmission**: HTTPS enforcement
- **Password Security**: bcrypt hashing with salt
- **Session Security**: HTTP-only secure cookies
- **Input Sanitization**: XSS and injection prevention

### Access Control
- **Application Password**: Entry-level protection
- **User Authentication**: JWT-based sessions
- **Role-Based Access**: Admin and user permissions
- **Rate Limiting**: API abuse prevention
- **File Upload Security**: Restricted file types and sizes

### Security Headers
- **Content Security Policy**: XSS protection
- **HSTS**: HTTPS enforcement
- **X-Frame-Options**: Clickjacking prevention
- **X-Content-Type-Options**: MIME sniffing protection

### Monitoring & Logging
- **Security Event Logging**: Authentication attempts
- **Error Tracking**: Anonymized error logs
- **Access Monitoring**: Session tracking
- **API Usage Tracking**: Rate limit monitoring

## AI Integration

### OpenAI Integration
- **GPT-4**: Primary conversation model
- **Whisper**: Speech recognition
- **TTS**: Text-to-speech synthesis
- **Error Handling**: Graceful degradation

### Claude Integration
- **Fallback Provider**: Automatic switching
- **Model Mapping**: Compatible model selection
- **Message Format Conversion**: OpenAI to Claude format
- **Response Normalization**: Consistent output format

### Prompt Engineering
- **System Prompts**: Investment-focused personality
- **Context Integration**: Market conversation history
- **Lead Qualification**: Structured information gathering
- **Response Formatting**: Plain text without markdown

### Smart Context Loading
- **Keyword Extraction**: Relevant conversation identification
- **Relevance Scoring**: Weighted context selection
- **Token Management**: Efficient context window usage
- **Fallback Strategy**: Recent conversations when no matches

## Voice Processing

### Speech-to-Text Pipeline
1. **Audio Capture**: MediaRecorder API
2. **Format Conversion**: WAV/WebM to compatible format
3. **API Transmission**: Secure upload to OpenAI Whisper
4. **Transcription Processing**: Real-time text conversion
5. **Error Handling**: Graceful fallback to text input

### Text-to-Speech Pipeline
1. **Text Preparation**: Response sanitization
2. **API Request**: OpenAI TTS with "alloy" voice
3. **Audio Generation**: High-quality speech synthesis
4. **Playback Management**: Automatic audio playback
5. **Visual Feedback**: Blob animation during speech

### Audio Management
- **Format Support**: WAV, MP3, WebM, OGG
- **Quality Settings**: 44.1kHz sample rate, mono channel
- **Noise Reduction**: Echo cancellation and noise suppression
- **Browser Compatibility**: Cross-browser audio support

## Stock Detection System

### Detection Methods
1. **Ticker Symbol Detection**: Regex pattern matching (95% confidence)
2. **Company Name Detection**: Exact name matching (85% confidence)
3. **Keyword Detection**: Industry term mapping (70% confidence)

### Stock Database
- **Coverage**: 10+ major stocks (AAPL, TSLA, NVDA, etc.)
- **Data Points**: Price, market cap, analyst ratings, thesis
- **Keywords**: 119+ mapped terms for detection
- **Update Frequency**: Static data with timestamp tracking

### Preview System
- **Automatic Display**: During AI speech
- **Visual Design**: Minimal, non-intrusive overlay
- **Animation**: Smooth fade-in/fade-out transitions
- **Positioning**: Bottom-left, above status indicator

### Performance Optimization
- **Efficient Matching**: Optimized regex patterns
- **Memory Management**: Map-based duplicate prevention
- **Speed**: <1ms processing for typical responses
- **Scalability**: Handles 10,000+ character texts

## Conversation Management

### Storage Architecture
- **Dual Format**: JSON (encrypted) + TXT (readable)
- **Encryption**: Field-level AES-256 encryption
- **Retention**: Last 100 conversations maximum
- **Backup**: Automatic text file generation

### History Features
- **Browsing Interface**: Modal with grouped conversations
- **Search Capability**: Keyword-based conversation finding
- **Resume Functionality**: Continue previous discussions
- **Export Options**: Human-readable text format

### Collective Chat Integration
- **Community Data**: Investment discussion archives
- **Smart Loading**: Relevance-based context retrieval
- **Attribution System**: Message source tracking
- **Market Insights**: Real-time investment community data

### Context Management
- **Relevance Scoring**: Keyword-based conversation ranking
- **Token Optimization**: Efficient context window usage
- **Fallback Strategy**: Recent conversations when no matches
- **Performance**: Sub-second context loading

## User Workflows

### New User Onboarding
1. **Access Control**: Enter application password
2. **Welcome Experience**: Automatic voice greeting
3. **Prompt Selection**: Choose conversation starter
4. **Voice/Text Choice**: Select interaction method
5. **AI Introduction**: Investment-focused greeting

### Voice Interaction Flow
1. **Activation**: Click microphone button
2. **Recording**: Speak message with visual feedback
3. **Processing**: Speech-to-text conversion
4. **AI Response**: Intelligent reply generation
5. **Speech Output**: Text-to-speech playback
6. **Stock Preview**: Automatic stock card display
7. **Continuation**: Seamless conversation flow

### Text Interaction Flow
1. **Input**: Type message in text field
2. **Submission**: Click send or press Enter
3. **Processing**: Direct AI processing
4. **Response**: Text and audio output
5. **Stock Detection**: Automatic stock preview
6. **History**: Conversation logging

### Lead Qualification Process
1. **Rapport Building**: Natural conversation start
2. **Experience Assessment**: Investment background inquiry
3. **Goal Identification**: Investment objectives discussion
4. **Portfolio Evaluation**: Size and risk tolerance
5. **Contact Collection**: Natural information gathering
6. **Qualification Scoring**: Automated lead ranking
7. **Follow-up Scheduling**: Consultation booking

### Conversation History Management
1. **History Access**: Click history button
2. **Browse Conversations**: Grouped by session
3. **Search Functionality**: Keyword-based finding
4. **Conversation Selection**: Choose previous discussion
5. **Resume Capability**: Continue where left off
6. **Export Options**: Download readable format

## Deployment Architecture

### Production Environment
- **Platform**: Node.js server environment
- **Process Management**: PM2 or similar
- **Reverse Proxy**: Nginx for static files and SSL
- **SSL/TLS**: Let's Encrypt or commercial certificate
- **Environment Variables**: Secure configuration management

### Scalability Considerations
- **Horizontal Scaling**: Multiple server instances
- **Load Balancing**: Request distribution
- **Database Migration**: Future database integration
- **CDN Integration**: Static asset delivery
- **Caching Strategy**: Response and asset caching

### Monitoring & Maintenance
- **Health Checks**: Automated service monitoring
- **Log Management**: Centralized logging system
- **Error Tracking**: Real-time error monitoring
- **Performance Metrics**: Response time and usage analytics
- **Security Monitoring**: Intrusion detection and prevention

### Backup & Recovery
- **Data Backup**: Automated conversation history backup
- **Configuration Backup**: Environment and settings
- **Recovery Procedures**: Disaster recovery planning
- **Version Control**: Code and configuration versioning

---

*This specification document provides a comprehensive overview of the Echo application architecture, features, and implementation details. For technical implementation details, refer to the source code and additional documentation in the `/docs` folder.*
