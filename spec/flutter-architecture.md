# Flutter Architecture Document - Echo Mobile App

## Overview

This document outlines the detailed Flutter architecture for the Echo (ShareFlix) mobile applications, including project structure, state management patterns, widget hierarchy, and platform-specific implementations.

## Architecture Principles

### Clean Architecture Layers
1. **Presentation Layer**: UI widgets and state management
2. **Domain Layer**: Business logic and use cases  
3. **Data Layer**: API clients, local storage, and repositories

### State Management Strategy
- **Riverpod**: Primary state management solution
- **Reactive Programming**: Stream-based data flow
- **Immutable State**: All state objects are immutable
- **Provider Composition**: Modular provider architecture

## Project Structure

```
flutter_echo/
├── lib/
│   ├── main.dart                        # App entry point
│   ├── app.dart                         # App configuration
│   ├── core/                           # Core functionality
│   │   ├── api/
│   │   │   ├── api_client.dart         # HTTP client with interceptors
│   │   │   ├── api_endpoints.dart      # Endpoint definitions
│   │   │   ├── api_exceptions.dart     # Custom exceptions
│   │   │   └── models/
│   │   │       ├── api_response.dart   # Generic API response
│   │   │       └── error_response.dart # Error response model
│   │   ├── constants/
│   │   │   ├── app_constants.dart      # App-wide constants
│   │   │   ├── api_constants.dart      # API URLs and keys
│   │   │   ├── colors.dart             # Color palette
│   │   │   └── strings.dart            # Localized strings
│   │   ├── utils/
│   │   │   ├── audio_utils.dart        # Audio processing helpers
│   │   │   ├── date_utils.dart         # Date formatting
│   │   │   ├── validation_utils.dart   # Input validation
│   │   │   └── encryption_utils.dart   # Client-side encryption
│   │   ├── storage/
│   │   │   ├── local_storage.dart      # Hive/SQLite wrapper
│   │   │   ├── cache_manager.dart      # Offline data management
│   │   │   └── secure_storage.dart     # Sensitive data storage
│   │   └── services/
│   │       ├── permission_service.dart # Platform permissions
│   │       ├── notification_service.dart # Push notifications
│   │       └── analytics_service.dart  # Usage analytics
│   ├── features/                       # Feature modules
│   │   ├── auth/                       # Authentication feature
│   │   │   ├── data/
│   │   │   │   ├── models/
│   │   │   │   │   ├── user_model.dart
│   │   │   │   │   └── session_model.dart
│   │   │   │   ├── repositories/
│   │   │   │   │   └── auth_repository.dart
│   │   │   │   └── services/
│   │   │   │       └── auth_api_service.dart
│   │   │   ├── domain/
│   │   │   │   ├── entities/
│   │   │   │   │   ├── user.dart
│   │   │   │   │   └── session.dart
│   │   │   │   └── usecases/
│   │   │   │       ├── login_usecase.dart
│   │   │   │       └── verify_access_usecase.dart
│   │   │   └── presentation/
│   │   │       ├── providers/
│   │   │       │   └── auth_provider.dart
│   │   │       ├── widgets/
│   │   │       │   ├── access_control_screen.dart
│   │   │       │   └── login_form.dart
│   │   │       └── screens/
│   │   │           └── auth_screen.dart
│   │   ├── chat/                       # Chat conversation feature
│   │   │   ├── data/
│   │   │   │   ├── models/
│   │   │   │   │   ├── message_model.dart
│   │   │   │   │   └── conversation_model.dart
│   │   │   │   ├── repositories/
│   │   │   │   │   └── chat_repository.dart
│   │   │   │   └── services/
│   │   │   │       └── chat_api_service.dart
│   │   │   ├── domain/
│   │   │   │   ├── entities/
│   │   │   │   │   ├── message.dart
│   │   │   │   │   └── conversation.dart
│   │   │   │   └── usecases/
│   │   │   │       ├── send_message_usecase.dart
│   │   │   │       └── get_ai_response_usecase.dart
│   │   │   └── presentation/
│   │   │       ├── providers/
│   │   │       │   └── conversation_provider.dart
│   │   │       ├── widgets/
│   │   │       │   ├── message_bubble.dart
│   │   │       │   ├── chat_input.dart
│   │   │       │   └── typing_indicator.dart
│   │   │       └── screens/
│   │   │           └── chat_screen.dart
│   │   ├── voice/                      # Voice recording feature
│   │   │   ├── data/
│   │   │   │   ├── models/
│   │   │   │   │   └── audio_recording_model.dart
│   │   │   │   ├── repositories/
│   │   │   │   │   └── voice_repository.dart
│   │   │   │   └── services/
│   │   │   │       ├── audio_service.dart
│   │   │   │       └── speech_api_service.dart
│   │   │   ├── domain/
│   │   │   │   ├── entities/
│   │   │   │   │   └── audio_recording.dart
│   │   │   │   └── usecases/
│   │   │   │       ├── record_audio_usecase.dart
│   │   │   │       └── transcribe_audio_usecase.dart
│   │   │   └── presentation/
│   │   │       ├── providers/
│   │   │       │   └── voice_provider.dart
│   │   │       ├── widgets/
│   │   │       │   ├── voice_button.dart
│   │   │       │   ├── blob_animation.dart
│   │   │       │   └── audio_visualizer.dart
│   │   │       └── screens/
│   │   │           └── voice_screen.dart
│   │   ├── stocks/                     # Stock detection feature
│   │   │   ├── data/
│   │   │   │   ├── models/
│   │   │   │   │   └── stock_model.dart
│   │   │   │   ├── repositories/
│   │   │   │   │   └── stock_repository.dart
│   │   │   │   └── services/
│   │   │   │       └── stock_api_service.dart
│   │   │   ├── domain/
│   │   │   │   ├── entities/
│   │   │   │   │   └── stock.dart
│   │   │   │   └── usecases/
│   │   │   │       └── detect_stocks_usecase.dart
│   │   │   └── presentation/
│   │   │       ├── providers/
│   │   │       │   └── stock_provider.dart
│   │   │       ├── widgets/
│   │   │       │   ├── stock_preview_card.dart
│   │   │       │   └── stock_chart.dart
│   │   │       └── screens/
│   │   │           └── stock_detail_screen.dart
│   │   ├── history/                    # Conversation history feature
│   │   │   ├── data/
│   │   │   │   ├── models/
│   │   │   │   │   └── conversation_group_model.dart
│   │   │   │   ├── repositories/
│   │   │   │   │   └── history_repository.dart
│   │   │   │   └── services/
│   │   │   │       └── history_api_service.dart
│   │   │   ├── domain/
│   │   │   │   ├── entities/
│   │   │   │   │   └── conversation_group.dart
│   │   │   │   └── usecases/
│   │   │   │       ├── get_conversation_history_usecase.dart
│   │   │   │       └── resume_conversation_usecase.dart
│   │   │   └── presentation/
│   │   │       ├── providers/
│   │   │       │   └── history_provider.dart
│   │   │       ├── widgets/
│   │   │       │   ├── conversation_group_tile.dart
│   │   │       │   └── history_search_bar.dart
│   │   │       └── screens/
│   │   │           └── history_screen.dart
│   │   └── onboarding/                 # Welcome and onboarding
│   │       ├── data/
│   │       │   └── services/
│   │       │       └── welcome_audio_service.dart
│   │       ├── domain/
│   │       │   └── usecases/
│   │       │       └── play_welcome_message_usecase.dart
│   │       └── presentation/
│   │           ├── providers/
│   │           │   └── onboarding_provider.dart
│   │           ├── widgets/
│   │           │   ├── welcome_animation.dart
│   │           │   └── prompt_button.dart
│   │           └── screens/
│   │               └── onboarding_screen.dart
│   ├── shared/                         # Shared components
│   │   ├── widgets/
│   │   │   ├── custom_button.dart      # Reusable button component
│   │   │   ├── loading_indicator.dart  # Loading animations
│   │   │   ├── error_dialog.dart       # Error handling UI
│   │   │   ├── custom_text_field.dart  # Styled text input
│   │   │   └── animated_container.dart # Custom animations
│   │   ├── theme/
│   │   │   ├── app_theme.dart          # Material theme definition
│   │   │   ├── text_styles.dart        # Typography styles
│   │   │   └── dimensions.dart         # Spacing and sizing
│   │   └── extensions/
│   │       ├── context_extensions.dart # BuildContext extensions
│   │       └── string_extensions.dart  # String utility extensions
│   └── providers/                      # Global providers
│       ├── app_state_provider.dart     # Global app state
│       └── dependency_injection.dart   # DI container setup
├── test/                              # Test files
│   ├── unit/                          # Unit tests
│   ├── widget/                        # Widget tests
│   └── integration/                   # Integration tests
├── assets/                            # Static assets
│   ├── images/                        # Image assets
│   ├── icons/                         # Icon assets
│   └── audio/                         # Audio assets
├── android/                           # Android-specific code
├── ios/                              # iOS-specific code
└── pubspec.yaml                      # Dependencies and configuration
```

## State Management Architecture

### Provider Hierarchy
```dart
// Root provider setup
final appStateProvider = StateNotifierProvider<AppStateNotifier, AppState>((ref) {
  return AppStateNotifier();
});

// Feature-specific providers
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  return AuthNotifier(ref.read(authRepositoryProvider));
});

final conversationProvider = StateNotifierProvider<ConversationNotifier, ConversationState>((ref) {
  return ConversationNotifier(ref.read(chatRepositoryProvider));
});

final voiceProvider = StateNotifierProvider<VoiceNotifier, VoiceState>((ref) {
  return VoiceNotifier(ref.read(voiceRepositoryProvider));
});

final stockProvider = StateNotifierProvider<StockNotifier, StockState>((ref) {
  return StockNotifier(ref.read(stockRepositoryProvider));
});
```

### State Models
```dart
// Conversation State
@freezed
class ConversationState with _$ConversationState {
  const factory ConversationState({
    @Default([]) List<Message> messages,
    @Default([]) List<ConversationGroup> history,
    @Default(false) bool isLoading,
    @Default(false) bool isSending,
    String? error,
    String? currentSessionId,
    LeadData? leadData,
  }) = _ConversationState;
}

// Voice State
@freezed
class VoiceState with _$VoiceState {
  const factory VoiceState({
    @Default(false) bool isRecording,
    @Default(false) bool isListening,
    @Default(false) bool isPlaying,
    String? currentAudioUrl,
    @Default(0.0) double recordingDuration,
    VoiceError? error,
  }) = _VoiceState;
}

// Stock State
@freezed
class StockState with _$StockState {
  const factory StockState({
    @Default([]) List<Stock> detectedStocks,
    Stock? previewStock,
    @Default(false) bool isDetecting,
    String? error,
  }) = _StockState;
}
```

## Widget Architecture

### Screen Composition Pattern
```dart
// Main chat screen structure
class ChatScreen extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: ChatAppBar(),
      body: Column(
        children: [
          Expanded(child: ConversationView()),
          StockPreviewOverlay(),
          ChatInputSection(),
        ],
      ),
      floatingActionButton: VoiceButton(),
    );
  }
}

// Conversation view with message list
class ConversationView extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final conversationState = ref.watch(conversationProvider);
    
    return ListView.builder(
      itemCount: conversationState.messages.length,
      itemBuilder: (context, index) {
        final message = conversationState.messages[index];
        return MessageBubble(
          message: message,
          isUser: message.isUser,
        );
      },
    );
  }
}
```

### Custom Widget Components
```dart
// Voice button with animation
class VoiceButton extends ConsumerStatefulWidget {
  @override
  _VoiceButtonState createState() => _VoiceButtonState();
}

class _VoiceButtonState extends ConsumerState<VoiceButton> 
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  
  @override
  Widget build(BuildContext context) {
    final voiceState = ref.watch(voiceProvider);
    
    return GestureDetector(
      onTap: () => ref.read(voiceProvider.notifier).toggleRecording(),
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: voiceState.isRecording 
                    ? Colors.red 
                    : Theme.of(context).primaryColor,
              ),
              child: Icon(
                voiceState.isRecording ? Icons.stop : Icons.mic,
                color: Colors.white,
                size: 32,
              ),
            ),
          );
        },
      ),
    );
  }
}

// Stock preview card with animation
class StockPreviewCard extends ConsumerWidget {
  final Stock stock;
  
  const StockPreviewCard({required this.stock});
  
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AnimatedContainer(
      duration: Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      child: Card(
        elevation: 8,
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    stock.companyName,
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  Text(
                    stock.ticker,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Theme.of(context).primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 8),
              Row(
                children: [
                  Text(
                    '\$${stock.price.toStringAsFixed(2)}',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  SizedBox(width: 8),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: stock.changePercent >= 0 ? Colors.green : Colors.red,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      '${stock.changePercent >= 0 ? '+' : ''}${stock.changePercent.toStringAsFixed(2)}%',
                      style: TextStyle(color: Colors.white, fontSize: 12),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
```

## Platform-Specific Implementation

### Audio Service (iOS/Android)
```dart
// Audio service with platform-specific implementations
class AudioService {
  FlutterSoundRecorder? _recorder;
  FlutterSoundPlayer? _player;
  
  Future<void> initialize() async {
    _recorder = FlutterSoundRecorder();
    _player = FlutterSoundPlayer();
    
    await _recorder!.openRecorder();
    await _player!.openPlayer();
  }
  
  Future<void> startRecording() async {
    await _recorder!.startRecorder(
      toFile: 'temp_audio.wav',
      codec: Codec.pcm16WAV,
      sampleRate: 44100,
      numChannels: 1,
    );
  }
  
  Future<String?> stopRecording() async {
    return await _recorder!.stopRecorder();
  }
  
  Future<void> playAudio(String url) async {
    await _player!.startPlayer(
      fromURI: url,
      codec: Codec.mp3,
    );
  }
  
  void dispose() {
    _recorder?.closeRecorder();
    _player?.closePlayer();
  }
}
```

### Permission Handling
```dart
// Permission service for microphone and storage access
class PermissionService {
  Future<bool> requestMicrophonePermission() async {
    final status = await Permission.microphone.request();
    return status == PermissionStatus.granted;
  }
  
  Future<bool> requestStoragePermission() async {
    if (Platform.isAndroid) {
      final status = await Permission.storage.request();
      return status == PermissionStatus.granted;
    }
    return true; // iOS doesn't require explicit storage permission
  }
  
  Future<bool> checkPermissions() async {
    final micPermission = await Permission.microphone.status;
    final storagePermission = Platform.isAndroid 
        ? await Permission.storage.status 
        : PermissionStatus.granted;
    
    return micPermission == PermissionStatus.granted && 
           storagePermission == PermissionStatus.granted;
  }
}
```

### Local Storage with Hive
```dart
// Conversation entity for local storage
@HiveType(typeId: 0)
class ConversationEntity extends HiveObject {
  @HiveField(0)
  String id;
  
  @HiveField(1)
  String sessionId;
  
  @HiveField(2)
  List<MessageEntity> messages;
  
  @HiveField(3)
  DateTime timestamp;
  
  @HiveField(4)
  bool synced;
  
  @HiveField(5)
  Map<String, dynamic>? leadData;
  
  ConversationEntity({
    required this.id,
    required this.sessionId,
    required this.messages,
    required this.timestamp,
    this.synced = false,
    this.leadData,
  });
}

// Local storage service
class LocalStorageService {
  static const String conversationsBox = 'conversations';
  static const String settingsBox = 'settings';
  
  Future<void> initialize() async {
    await Hive.initFlutter();
    Hive.registerAdapter(ConversationEntityAdapter());
    Hive.registerAdapter(MessageEntityAdapter());
    
    await Hive.openBox<ConversationEntity>(conversationsBox);
    await Hive.openBox(settingsBox);
  }
  
  Future<void> saveConversation(ConversationEntity conversation) async {
    final box = Hive.box<ConversationEntity>(conversationsBox);
    await box.put(conversation.id, conversation);
  }
  
  List<ConversationEntity> getConversations() {
    final box = Hive.box<ConversationEntity>(conversationsBox);
    return box.values.toList();
  }
  
  Future<void> clearConversations() async {
    final box = Hive.box<ConversationEntity>(conversationsBox);
    await box.clear();
  }
}
```

## Navigation Architecture

### Route Management
```dart
// App router with go_router
final appRouter = GoRouter(
  initialLocation: '/access',
  routes: [
    GoRoute(
      path: '/access',
      builder: (context, state) => AccessControlScreen(),
    ),
    GoRoute(
      path: '/onboarding',
      builder: (context, state) => OnboardingScreen(),
    ),
    GoRoute(
      path: '/chat',
      builder: (context, state) => ChatScreen(),
      routes: [
        GoRoute(
          path: '/history',
          builder: (context, state) => HistoryScreen(),
        ),
        GoRoute(
          path: '/stock/:ticker',
          builder: (context, state) => StockDetailScreen(
            ticker: state.params['ticker']!,
          ),
        ),
      ],
    ),
  ],
);
```

## Testing Strategy

### Unit Tests
```dart
// Provider unit tests
void main() {
  group('ConversationProvider Tests', () {
    late ConversationNotifier notifier;
    late MockChatRepository mockRepository;
    
    setUp(() {
      mockRepository = MockChatRepository();
      notifier = ConversationNotifier(mockRepository);
    });
    
    test('should send message successfully', () async {
      // Arrange
      const message = 'Test message';
      when(mockRepository.sendMessage(any))
          .thenAnswer((_) async => 'AI response');
      
      // Act
      await notifier.sendMessage(message);
      
      // Assert
      expect(notifier.state.messages.length, 2);
      expect(notifier.state.messages.first.content, message);
    });
  });
}
```

### Widget Tests
```dart
// Widget testing
void main() {
  group('VoiceButton Widget Tests', () {
    testWidgets('should show microphone icon when not recording', (tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: VoiceButton(),
          ),
        ),
      );
      
      expect(find.byIcon(Icons.mic), findsOneWidget);
      expect(find.byIcon(Icons.stop), findsNothing);
    });
  });
}
```

---

*This architecture provides a scalable, maintainable foundation for the Echo Flutter mobile applications while following Flutter best practices and clean architecture principles.*
