# Echo Flutter Mobile Migration Plan

## Executive Summary

This document outlines the comprehensive migration strategy for transforming the Echo (ShareFlix) voice-driven investment platform from a web application to native iOS and Android mobile applications using Flutter, while maintaining the existing Node.js backend.

**Timeline**: 12 weeks (3 months)  
**Approach**: Backend-first migration with Flutter frontend  
**Strategy**: Maintain web app alongside mobile apps  

## 1. Architecture Analysis & Current State Assessment

### Frontend Functionality Inventory (public/app.js - 3,690 lines)

#### **Core Application Class: VoiceLeadApp**
- **State Management**: 15+ instance variables tracking recording, listening, audio, conversations
- **Session Management**: sessionId, currentSessionId, conversationHistory array
- **Audio Context**: audioContextUnlocked, userHasInteracted flags
- **Lead Data**: leadData object for qualification tracking

#### **Voice Processing Pipeline**
```javascript
// Current Client-Side Voice Flow
toggleRecording() → startVoiceMode() → startRecording() → 
MediaRecorder API → processRecording() → speechToText() → 
getAIResponse() → textToSpeech() → extractLeadInfo()
```

**Functions to Migrate:**
- `startRecording()` - MediaRecorder setup and audio capture
- `processRecording()` - Audio blob processing and API calls
- `speechToText()` - OpenAI Whisper API integration
- `textToSpeech()` - OpenAI TTS API integration
- `prepareAudioForTTS()` - Audio element management

#### **Conversation Management (Client-Side Business Logic)**
```javascript
// Functions requiring backend migration
groupConversations(conversations)     // Groups by session/time - 50 lines
loadConversationContext(group)        // Context loading - 20 lines
continueConversation(sessionId)       // Resume logic - 30 lines
renderConversationHistory(convs)      // History rendering - 60 lines
```

#### **Stock Detection & Preview System**
```javascript
// Client-side stock detection (basic patterns)
detectStocksInResponse(text)          // Pattern matching - 40 lines
showStockPreview(stockData)           // Preview card display - 30 lines
handleStockPreviewForSpeech(text)     // Speech integration - 25 lines
```

#### **Lead Qualification Engine**
```javascript
// Business logic currently in frontend
extractLeadInfo(userMsg, aiResponse)  // Lead extraction - 80 lines
calculateQualificationScore(data)     // Scoring algorithm - 40 lines
updateLeadData(newData)              // Data management - 20 lines
```

#### **UI State Management**
```javascript
// Presentation logic (stays in Flutter)
updateUI(state)                      // Visual state updates
showAIThinking(message)              // Blob animation
hideAIThinking()                     // Animation cleanup
updateStatus(message)                // Status indicator
showError(title, message, details)   // Error dialogs
```

### Current API Endpoints Analysis

#### **Existing Endpoints (server.js)**
```javascript
// Voice Processing
POST /api/speech-to-text           // ✅ Ready for mobile
POST /api/text-to-speech           // ✅ Ready for mobile

// AI Chat
POST /api/chat                     // ⚠️ Needs enhancement for mobile
GET /api/ai-status                 // ✅ Ready for mobile
GET /api/ai-health                 // ✅ Ready for mobile

// Stock Detection
POST /api/detect-stocks            // ⚠️ Basic implementation
GET /api/test-stock-detection      // ✅ Testing endpoint

// Conversation History
GET /api/conversation-history      // ⚠️ Returns raw data, needs grouping
DELETE /api/conversation-history   // ✅ Ready for mobile
GET /api/conversation-history/download // ✅ Ready for mobile

// Authentication & Access
POST /api/access/verify            // ✅ Ready for mobile
GET /api/access/status             // ✅ Ready for mobile
POST /api/auth/register            // ✅ Ready for mobile
POST /api/auth/login               // ✅ Ready for mobile
```

## 2. Backend API Enhancement Plan

### **Critical API Gaps for Mobile Support**

#### **A. Enhanced Conversation Management**
```javascript
// NEW: Grouped conversation history
GET /api/conversation-history/grouped
Response: {
  success: true,
  conversationGroups: [
    {
      sessionId: "session_123",
      title: "Investment Goals Discussion",
      preview: "I'm interested in long-term growth...",
      startTime: "2025-01-01T10:00:00Z",
      messageCount: 8,
      messages: [...]
    }
  ]
}

// NEW: Resume conversation with context
POST /api/conversation/continue/:sessionId
Request: { sessionId: "session_123" }
Response: {
  success: true,
  conversationContext: [...],
  leadData: {...},
  sessionState: {...}
}

// NEW: Server-side conversation search
GET /api/conversation/search?query=stocks&limit=20
Response: {
  success: true,
  results: [...],
  relevanceScores: [...]
}
```

#### **B. Enhanced Stock Detection & Preview**
```javascript
// NEW: Combined stock detection for conversations
POST /api/detect-stocks-in-conversation
Request: {
  userMessage: "What do you think about Apple?",
  aiResponse: "Apple (AAPL) is a strong company..."
}
Response: {
  success: true,
  detectedStocks: [
    {
      ticker: "AAPL",
      companyName: "Apple Inc.",
      price: 189.84,
      changePercent: 2.34,
      matchDetails: { confidence: 0.95, source: "ai_response" },
      previewData: { /* complete stock info */ }
    }
  ]
}

// NEW: Complete stock preview data
GET /api/stock-preview/:ticker
Response: {
  success: true,
  stock: { /* complete stock object with all display data */ }
}
```

#### **C. Lead Qualification API**
```javascript
// NEW: Server-side lead extraction
POST /api/lead/extract
Request: {
  userMessage: "I have $100k to invest",
  aiResponse: "That's a substantial portfolio...",
  conversationHistory: [...]
}
Response: {
  success: true,
  extractedData: {
    portfolioSize: 100000,
    experience: "intermediate",
    confidence: 0.85
  },
  qualificationScore: 75,
  nextQuestions: [...]
}

// NEW: Lead qualification scoring
GET /api/lead/qualification-score?userId=123
Response: {
  success: true,
  score: 85,
  factors: {
    portfolioSize: 30,
    experience: 25,
    engagement: 30
  },
  recommendation: "high_priority"
}
```

#### **D. Mobile-Optimized Audio Processing**
```javascript
// NEW: Combined voice message processing
POST /api/audio/process-voice-message
Request: FormData with audio file + session context
Response: {
  success: true,
  transcription: "What stocks should I buy?",
  aiResponse: "Based on your goals...",
  audioUrl: "/api/audio/response/abc123.mp3",
  detectedStocks: [...],
  leadData: {...},
  sessionUpdated: true
}

// NEW: Cached welcome message
GET /api/audio/welcome-message
Response: Audio file (cached) with proper headers
```

#### **E. Session & State Management**
```javascript
// NEW: Mobile session management
POST /api/session/create
Request: { deviceId: "device_123", platform: "ios" }
Response: {
  success: true,
  sessionId: "session_456",
  sessionToken: "jwt_token",
  expiresAt: "2025-01-02T10:00:00Z"
}

// NEW: Session state persistence
PUT /api/session/state/:sessionId
Request: {
  conversationContext: [...],
  leadData: {...},
  uiState: { lastScreen: "chat", voiceEnabled: true }
}

GET /api/session/state/:sessionId
Response: {
  success: true,
  sessionState: { /* restored state */ }
}
```

### **API Design Principles for Mobile**

1. **Batch Operations**: Combine multiple operations in single requests
2. **Offline Support**: Include sync flags and conflict resolution
3. **Bandwidth Optimization**: Compress responses, paginate large datasets
4. **Error Handling**: Structured error codes for mobile error handling
5. **Caching**: Proper cache headers for offline capability

## 3. Flutter Mobile Application Architecture

### **Project Structure**
```
flutter_echo/
├── lib/
│   ├── main.dart
│   ├── core/
│   │   ├── api/
│   │   │   ├── api_client.dart          # HTTP client with auth
│   │   │   ├── endpoints.dart           # API endpoint definitions
│   │   │   └── models/                  # Request/response models
│   │   ├── constants/
│   │   │   ├── app_constants.dart       # App-wide constants
│   │   │   ├── colors.dart              # Color scheme
│   │   │   └── strings.dart             # Localized strings
│   │   ├── utils/
│   │   │   ├── audio_utils.dart         # Audio processing helpers
│   │   │   ├── encryption_utils.dart    # Client-side encryption
│   │   │   └── validation_utils.dart    # Input validation
│   │   └── storage/
│   │       ├── local_storage.dart       # SQLite/Hive wrapper
│   │       └── cache_manager.dart       # Offline data management
│   ├── features/
│   │   ├── auth/
│   │   │   ├── models/                  # User, Session models
│   │   │   ├── providers/               # Auth state management
│   │   │   ├── services/                # Auth API calls
│   │   │   └── widgets/                 # Login, access control UI
│   │   ├── chat/
│   │   │   ├── models/                  # Message, Conversation models
│   │   │   ├── providers/               # Chat state management
│   │   │   ├── services/                # Chat API integration
│   │   │   └── widgets/                 # Chat UI components
│   │   ├── voice/
│   │   │   ├── models/                  # Audio recording models
│   │   │   ├── providers/               # Voice state management
│   │   │   ├── services/                # Audio recording/playback
│   │   │   └── widgets/                 # Voice button, animations
│   │   ├── stocks/
│   │   │   ├── models/                  # Stock data models
│   │   │   ├── providers/               # Stock state management
│   │   │   ├── services/                # Stock API integration
│   │   │   └── widgets/                 # Stock preview cards
│   │   ├── history/
│   │   │   ├── models/                  # Conversation history models
│   │   │   ├── providers/               # History state management
│   │   │   ├── services/                # History API calls
│   │   │   └── widgets/                 # History UI components
│   │   └── onboarding/
│   │       ├── providers/               # Onboarding state
│   │       ├── services/                # Welcome audio service
│   │       └── widgets/                 # Welcome screens
│   ├── shared/
│   │   ├── widgets/
│   │   │   ├── custom_button.dart       # Reusable button component
│   │   │   ├── message_bubble.dart      # Chat message display
│   │   │   ├── loading_indicator.dart   # Loading animations
│   │   │   ├── error_dialog.dart        # Error handling UI
│   │   │   └── blob_animation.dart      # AI thinking animation
│   │   └── theme/
│   │       ├── app_theme.dart           # Material theme definition
│   │       └── text_styles.dart         # Typography styles
│   └── providers/
│       ├── app_state_provider.dart      # Global app state
│       ├── conversation_provider.dart   # Chat state management
│       ├── voice_provider.dart          # Voice recording state
│       ├── stock_provider.dart          # Stock detection state
│       └── session_provider.dart        # Session management
```

### **State Management with Riverpod**

#### **Core Providers**
```dart
// Session Management
final sessionProvider = StateNotifierProvider<SessionNotifier, SessionState>((ref) {
  return SessionNotifier(ref.read(apiClientProvider));
});

// Conversation State
final conversationProvider = StateNotifierProvider<ConversationNotifier, ConversationState>((ref) {
  return ConversationNotifier(ref.read(apiClientProvider));
});

// Voice Recording State
final voiceProvider = StateNotifierProvider<VoiceNotifier, VoiceState>((ref) {
  return VoiceNotifier(ref.read(audioServiceProvider));
});

// Stock Detection State
final stockProvider = StateNotifierProvider<StockNotifier, StockState>((ref) {
  return StockNotifier(ref.read(apiClientProvider));
});
```

#### **State Models**
```dart
// Conversation State
class ConversationState {
  final List<Message> messages;
  final List<ConversationGroup> history;
  final bool isLoading;
  final String? error;
  final String? currentSessionId;
  
  ConversationState({
    this.messages = const [],
    this.history = const [],
    this.isLoading = false,
    this.error,
    this.currentSessionId,
  });
}

// Voice State
class VoiceState {
  final bool isRecording;
  final bool isListening;
  final bool isPlaying;
  final String? currentAudioUrl;
  final VoiceError? error;
  
  VoiceState({
    this.isRecording = false,
    this.isListening = false,
    this.isPlaying = false,
    this.currentAudioUrl,
    this.error,
  });
}
```

### **Mobile-Specific Implementation Details**

#### **Voice Recording (iOS/Android)**
```dart
// Using flutter_sound package
class AudioService {
  FlutterSoundRecorder? _recorder;
  FlutterSoundPlayer? _player;
  
  Future<void> startRecording() async {
    await _recorder?.startRecorder(
      toFile: 'temp_audio.wav',
      codec: Codec.pcm16WAV,
      sampleRate: 44100,
      numChannels: 1,
    );
  }
  
  Future<String> stopRecording() async {
    final path = await _recorder?.stopRecorder();
    return path ?? '';
  }
  
  Future<void> playAudio(String url) async {
    await _player?.startPlayer(
      fromURI: url,
      codec: Codec.mp3,
    );
  }
}
```

#### **Offline Storage with Hive**
```dart
// Local conversation storage
@HiveType(typeId: 0)
class ConversationEntity extends HiveObject {
  @HiveField(0)
  String id;
  
  @HiveField(1)
  String sessionId;
  
  @HiveField(2)
  List<MessageEntity> messages;
  
  @HiveField(3)
  DateTime timestamp;
  
  @HiveField(4)
  bool synced;
}

// Offline sync service
class SyncService {
  Future<void> syncPendingMessages() async {
    final pendingMessages = await _localStorage.getPendingMessages();
    for (final message in pendingMessages) {
      try {
        await _apiClient.sendMessage(message);
        await _localStorage.markAsSynced(message.id);
      } catch (e) {
        // Handle sync failure
      }
    }
  }
}
```

#### **Platform-Specific Permissions**
```dart
// Permission handling
class PermissionService {
  Future<bool> requestMicrophonePermission() async {
    final status = await Permission.microphone.request();
    return status == PermissionStatus.granted;
  }
  
  Future<bool> requestStoragePermission() async {
    final status = await Permission.storage.request();
    return status == PermissionStatus.granted;
  }
}
```

## 4. Frontend-to-Backend Migration Matrix

| Frontend Function | Current Location | Migration Target | Priority | Effort | Justification |
|------------------|------------------|------------------|----------|--------|---------------|
| `groupConversations()` | app.js:2823 | `GET /api/conversation-history/grouped` | P0 | 2d | Business logic, needed for mobile |
| `extractLeadInfo()` | app.js:2685 | `POST /api/lead/extract` | P0 | 3d | Complex business logic |
| `calculateQualificationScore()` | app.js:2750 | `GET /api/lead/qualification-score` | P1 | 2d | Scoring algorithm |
| `loadConversationContext()` | app.js:3229 | `POST /api/conversation/continue` | P0 | 1d | Session management |
| `detectStocksInResponse()` | app.js:3663 | Enhanced `/api/detect-stocks` | P1 | 2d | Better detection needed |
| `showStockPreview()` | app.js:3343 | Stays client-side | - | - | Pure UI logic |
| `updateUI()` | app.js:2580 | Stays client-side | - | - | Presentation logic |
| `showAIThinking()` | app.js:3465 | Stays client-side | - | - | Animation logic |

**Priority Levels:**
- **P0**: Critical for mobile launch (blocks core functionality)
- **P1**: Important for feature parity (enhances user experience)
- **P2**: Nice to have (future enhancement)

## 5. Phased Migration Strategy

### **Phase 1: Backend API Foundation (Weeks 1-2)**

**Week 1: Core API Enhancements**
- Move `groupConversations()` logic to backend
- Create `GET /api/conversation-history/grouped` endpoint
- Implement `POST /api/conversation/continue/:sessionId`
- Add conversation search endpoint
- Update existing web client to use new endpoints

**Week 2: Lead & Stock APIs**
- Move `extractLeadInfo()` to `POST /api/lead/extract`
- Implement lead qualification scoring API
- Enhance stock detection with conversation context
- Create combined voice processing endpoint
- Add comprehensive API documentation

**Deliverables:**
- ✅ All new API endpoints functional
- ✅ Web client updated and tested
- ✅ API documentation complete
- ✅ Backward compatibility maintained

### **Phase 2: Flutter Project Setup & Core Features (Weeks 3-4)**

**Week 3: Project Foundation**
- Initialize Flutter project with recommended architecture
- Set up Riverpod state management
- Implement API client with authentication
- Create core data models (Message, Conversation, Stock, etc.)
- Build basic navigation structure

**Week 4: Core UI Components**
- Implement conversation display with message bubbles
- Create text input with send functionality
- Build basic chat functionality (text-only)
- Add access control and session management
- Implement error handling and loading states

**Deliverables:**
- ✅ Flutter project structure complete
- ✅ Basic chat functionality working
- ✅ API integration functional
- ✅ Core UI components implemented

### **Phase 3: Voice Integration (Weeks 5-6)**

**Week 5: Audio Recording**
- Implement voice recording with flutter_sound
- Add platform-specific permission handling
- Create voice button with visual feedback
- Integrate speech-to-text API calls
- Handle audio format conversion

**Week 6: Audio Playback & TTS**
- Implement TTS playback with queue management
- Add blob animation during AI speech
- Create audio session management
- Handle background audio and interruptions
- Test on both iOS and Android devices

**Deliverables:**
- ✅ Voice recording functional on both platforms
- ✅ TTS playback working smoothly
- ✅ Audio permissions properly handled
- ✅ Voice UI matches web experience

### **Phase 4: Advanced Features (Weeks 7-8)**

**Week 7: Stock Detection & History**
- Implement stock detection and preview cards
- Build conversation history with grouping
- Add search functionality
- Create resume conversation feature
- Implement offline conversation storage

**Week 8: Lead Qualification & Polish**
- Add lead qualification display
- Implement predefined prompts
- Create onboarding flow with welcome audio
- Add offline sync capability
- Performance optimization and testing

**Deliverables:**
- ✅ Feature parity with web application
- ✅ Stock preview cards functional
- ✅ Conversation history working
- ✅ Offline capability implemented

### **Phase 5: Testing & App Store Preparation (Weeks 9-10)**

**Week 9: Testing & Optimization**
- Comprehensive testing on multiple devices
- Performance optimization and memory management
- Bug fixes and edge case handling
- User acceptance testing
- Security review and penetration testing

**Week 10: App Store Preparation**
- Create app store assets (screenshots, descriptions)
- Implement app store compliance requirements
- Set up CI/CD pipeline for releases
- Beta testing through TestFlight and Google Play Beta
- Final polish and launch preparation

**Deliverables:**
- ✅ Apps ready for app store submission
- ✅ All tests passing
- ✅ Performance benchmarks met
- ✅ Beta testing complete

## 6. Success Criteria & Timeline

### **Technical Success Criteria**
- ✅ Feature parity with web application for core functionality
- ✅ Voice recording/playback <2s latency on both platforms
- ✅ App launch time <3s on mid-range devices
- ✅ Offline conversation viewing capability
- ✅ No regressions in web application functionality
- ✅ 95%+ crash-free rate in beta testing

### **Business Success Criteria**
- ✅ App store approval on first submission
- ✅ User satisfaction score >4.5/5 in beta testing
- ✅ Voice interaction success rate >90%
- ✅ Lead qualification accuracy maintained
- ✅ Mobile user engagement >web baseline

### **Timeline Summary**
- **Total Duration**: 12 weeks (3 months)
- **Backend Enhancement**: 2 weeks
- **Flutter Core Development**: 4 weeks  
- **Voice & Advanced Features**: 4 weeks
- **Testing & App Store Prep**: 2 weeks

### **Resource Requirements**
- **Backend Developer**: 2 weeks full-time
- **Flutter Developer**: 10 weeks full-time
- **QA Engineer**: 2 weeks (weeks 9-10)
- **DevOps Engineer**: 1 week (CI/CD setup)

This migration plan ensures a smooth transition from web to mobile while maintaining the voice-first nature and investment focus that makes Echo unique in the market.

---

*For detailed API specifications, Flutter architecture diagrams, and implementation examples, see the companion documents in this spec folder.*
